#region Using Statements
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Net;
using System.Net.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json.Linq;
using MimeKit;
using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.EntityFrameworkCore;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils._Helper;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Helpers.ViewModel.IdentityVM;
using Jobid.App.Tenant;
using Jobid.App.Helpers.Context;
using Google.Apis.Auth;
using Serilog;
using ILogger = Serilog.ILogger;
using System.Data;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Utility = Jobid.App.Helpers.Utils.Utility;
using Hangfire;
using Microsoft.AspNetCore.Hosting;
using System.IO;
using System.Web;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Helpers.Extensions;
using static Jobid.App.RabbitMQ.Records;
using RabbitMQ.Client;
using Jobid.App.RabbitMQ;
using Jobid.App.Helpers.Exceptions;
using WatchDog;
using Jobid.App.Tenant.ViewModel;
using AutoMapper;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.SignalR;
using Jobid.App.Notification.Hubs;
using Jobid.App.Helpers.Utils.Attributes;
using Microsoft.OpenApi.Extensions;
using Jobid.App.Tenant.Contract;
using static Jobid.App.JobProject.Enums.Enums;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Wiki.Services.Contract;
using Jobid.App.AdminConsole.Models;
#endregion

namespace Jobid.App.Helpers.Controllers
{
    [Route("api/[controller]")]
    [Produces("Application/json")]
    [ApiController]
    public class IdentityController : ControllerBase
    {
        #region Properties and Constructor
        private readonly IUnitofwork Services_Repo;
        private UserManager<User> UserManager;
        public readonly IConfiguration config;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private SignInManager<User> SignInManager;
        private ILogger _logger = Log.ForContext<IdentityController>();
        private const string LinkedinUserInfoParameters = "id,first-name,last-name,maiden-name,formatted-name,phonetic-first-name,phonetic-last-name,formatted-phonetic-name,headline,location,picture-url,industry,current-share,num-connections,num-connections-capped,summary,specialties,positions,picture-urls,site-standard-profile-request,api-standard-profile-request,public-profile-url,email-address,languages,skills";
        private ITenantSchema tenantSchema;
        private readonly string _conString;
        private readonly IEmailService _emailService;
        private readonly IWebHostEnvironment _environment;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IAdminService _adminService;
        private readonly IPublisherService _publisherService;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly CustomAppSettings _appSettings;
        private readonly IMapper _mapper;
        private readonly JobProDbContext _publicContext;
        private readonly IHubContext<SmartLoginHub, ISmartLoginClient> _hubContext;
        private readonly IBackgroundJobService _tenantBackgroundJobService;
        private readonly IWikiAccessService _wikiAccessService;

        public IdentityController(IUnitofwork unitofwork, UserManager<User> _usermanager, SignInManager<User> signInManager, IConfiguration config, IHttpContextAccessor httpContextAccessor, ITenantSchema tenantSchema, IEmailService emailService, IWebHostEnvironment environment, IWebHostEnvironment webHostEnvironment, IAdminService adminService, IPublisherService publisherService, IAWSS3Sevices aWSS3Sevices, IMapper mapper, JobProDbContext publicContext, IHubContext<SmartLoginHub, ISmartLoginClient> hubContext, IBackgroundJobService tenantBackgroundJobService, IWikiAccessService wikiAccessService)
        {
            this.Services_Repo = unitofwork;
            this.UserManager = _usermanager;
            this.SignInManager = signInManager;
            this.config = config;
            _httpContextAccessor = httpContextAccessor;
            this.tenantSchema = tenantSchema;
            _conString = GlobalVariables.ConnectionString;
            _emailService = emailService;
            _environment = environment;
            _webHostEnvironment = webHostEnvironment;
            _adminService = adminService;
            _publisherService = publisherService;
            _aWSS3Sevices = aWSS3Sevices;
            _appSettings = GlobalVariables.CustomAppSettings;
            _mapper = mapper;
            _publicContext = publicContext;
            _hubContext = hubContext;
            _tenantBackgroundJobService = tenantBackgroundJobService;
            _wikiAccessService = wikiAccessService;
        }
        #endregion

        #region Log Response Or Info
        private void LogResponseOrInfo(string methodName, object response = null, object payload = null)
        {
            var message = "";
            var responseString = "";
            if (response != null)
            {
                responseString = JsonConvert.SerializeObject(response);
                message = "Response";
            }

            if (payload != null)
            {
                responseString = JsonConvert.SerializeObject(payload);
                message = "Payload";
            }

            var payloadStr = JsonConvert.SerializeObject(payload);
            _logger.Information($"{message}: {responseString}", methodName);
        }
        #endregion

        #region Get company presigned url
        /// <summary>
        /// Get company presigned url
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("getcompanypresignedurl")]
        public async Task<IActionResult> GetCompanyPresignedUrl(string userId)
        {
            var presignedUrl = "";
            try
            {
                var subdomain = this.tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var userCompany = Services_Repo.UserCompaniesServices.GetUserCompany(userId, subdomain);
                presignedUrl = userCompany.tenant.LogoUrl;
                return Ok(new ApiResponse<UserCompaniesVM>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Presigned url retrieved successfully",
                    Data = userCompany
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString(), "GetCompanyPresignedUrl");
                return BadRequest(new ApiResponse<string>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Error retrieving presigned url",
                    Data = ""
                });
            }
        }
        #endregion

        #region Get All GMT Timezones And Offsets
        /// <summary>
        /// Gets All GMT Timezones And Offsets
        /// </summary>
        /// <returns></returns>
        [HttpGet("timezones/getallgmtoffsets")]
        public IActionResult GetGmtOffsets()
        {
            string filePath = Path.Combine(_webHostEnvironment.WebRootPath, "App", "Helpers", "HelperFiles", "timezones.json");

            if (!System.IO.File.Exists(filePath))
            {
                return NotFound();
            }

            string jsonContent = System.IO.File.ReadAllText(filePath);
            List<TimezoneDto> data = JsonConvert.DeserializeObject<List<TimezoneDto>>(jsonContent);

            return Ok(data);
        }
        #endregion

        #region Get User Companies
        /// <summary>
        /// This gets all the workspaces a user belongs to
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [Route("getusercompanies")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetUserCompanies()
        {
            try
            {
                var userId = User.GetUserId();
                var response = await Services_Repo.Userservice.GetUserCompanies(userId);
                LogResponseOrInfo("GetUserCompanies", response, null);
                return Ok(new ApiResponse<IEnumerable<UserCompanies>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Workspaces retrieved successfully",
                    Data = response
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString(), "GetUserCompanies");
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion

        #region Add Or Update User's Last Accessed Workspace
        /// <summary>
        /// Add or update user's last accessed workspace
        /// </summary>
        /// <param name="subdoamin"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("addlastaccessedworkspace")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> AddOrLastAccessedWorkspace(string subdoamin)
        {
            try
            {
                var userId = User.GetUserId();
                var response = await Services_Repo.UserCompaniesServices.AddOrUpdateDefaultCompany(userId, subdoamin);
                LogResponseOrInfo("AddOrLastAccessedWorkspace", response, subdoamin);
                return Ok(new ApiResponse<bool>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Workspace added successfully",
                    Data = response
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString(), "AddOrLastAccessedWorkspace", subdoamin);
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion

        #region Add user to waiting list
        /// <summary>
        /// Add user to waiting list
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddEmailToWaitingList")]
        public async Task<IActionResult> AddUserToWaitingList(WaitListDto model)
        {
            try
            {
                var result = await this.Services_Repo.Userservice.AddEmailToWaitList(model);
                return Ok(new ApiResponse<bool>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Successful",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString(), "AddUserToWaitingList", model);
                return Ok(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Get user from waiting list
        /// <summary>
        /// Get user from waiting list
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetWaitingList")]
        public async Task<IActionResult> GetUserFromWaitingList()
        {
            try
            {
                var result = await this.Services_Repo.Userservice.GetUserFromWaitList();
                return Ok(new ApiResponse<List<WaitingEmailList>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Successful",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString(), "GetUserFromWaitingList");
                return Ok(new ApiResponse<List<WaitingEmailList>>
                {
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = null
                });
            }
        }
        #endregion

        #region Get all users from all companies
        /// <summary>
        /// GetS all users from all companies
        /// </summary>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <param name="search"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllUsers")]
        public async Task<ApiResponse<Page<User>>> GetAllUsers(
           [FromQuery] int pageSize = 20,
           [FromQuery] int pageNumber = 1,
           [FromQuery] string search = null)
        {
            try
            {
                var region = Region.Africa;
                var userRegion = User.GetRegion();
                if (userRegion != null) region = userRegion.ToEnum<Region>();


                var result = await this.Services_Repo.Userservice.GetAllUsers(region, pageNumber, pageSize, search);
                return new ApiResponse<Page<User>>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString(), "GetAllUsers");
                return new ApiResponse<Page<User>>
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                };
            }
        }
        #endregion

        #region Get user by Id
        /// <summary>
        /// Get User by id
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserById/{Id}")]
        public async Task<ActionResult<User>> GetById(string Id)
        {
            if (Id == null) { return BadRequest(new { Mgs = " User Id Is Required !" }); }

            try
            {
                var Result = await this.UserManager.FindByIdAsync(Id);

                if (Result == null) { return NotFound(new { Mgs = " User Not Found !" }); }

                else { return Ok(Result); }

            }
            catch (Exception Ex)
            {

                return BadRequest();
            }
        }
        #endregion

        #region Add or Update user online status
        /// <summary>
        ///  Get user online status
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("AddOrUpdateUserOnlineStatus")]
        public async Task<IActionResult> AddOrUpdateUserOnlineStatus([FromQuery] UserOnlineStatusOptions status)
        {
            var userId = User.GetUserId();
            var result = await this.Services_Repo.Userservice.AddOrUpdateUserOnlineStatus(userId, status);
            return Ok(result);
        }
        #endregion

        #region Get user online status
        /// <summary>
        /// Get user online status
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [Route("GetUserOnlineStatus")]
        public async Task<IActionResult> GetUserOnlineStatus()
        {
            var userId = User.GetUserId();
            var result = await this.Services_Repo.Userservice.GetUserOnlineStatus(userId);
            return Ok(result);
        }
        #endregion

        #region Get userProfile by Id
        /// <summary>
        /// Get User by id
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserProfileById/{Id}")]
        public async Task<ActionResult> GetUserProfileById(string Id)
        {
            var Result = await this.Services_Repo.UserProfileServices.GetTenantUserProfileFromPublicUserId(Id);
            if (Result == null) { return NotFound(new { Mgs = " User Not Found !" }); }

            else { return Ok(Result); }
        }
        #endregion

        #region Get all users on jobpro with their copany details
        /// <summary>
        /// Get User by id
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllUsersOnJobpro")]
        public async Task<ActionResult> GetAllUsersOnJobproWithComDetails()
        {
            var Result = await this.Services_Repo.UserProfileServices.GetAllUserInJobproWithCompanyDetails();
            return Ok(Result);
        }
        #endregion

        #region Get countries
        /// <summary>
        /// Get countries
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetCountries")]
        public async Task<IActionResult> GetCountries()
        {
            var countries = await this.Services_Repo.Userservice.GetCountries();

            return Ok(countries.Select(x => new
            {
                Name = x.Name,
                Code = x.Code,
            }));
        }
        #endregion

        #region Get user by email
        /// <summary>
        /// Get user by email
        /// </summary>
        /// <param name="Model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetUserByEmail")]
        public async Task<ActionResult<User>> GetUserByEmail([FromBody] Emailmodel Model)
        {
            if (!ModelState.IsValid) { return BadRequest(); }

            User User = await this.UserManager.FindByEmailAsync(Model.Email);

            if (User != null) { return Ok(User); } else { return BadRequest("User Not Found"); }
        }
        #endregion

        #region Post User Name
        /// <summary>
        /// Post User Name
        /// </summary>
        /// <param name="Model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetUserName")]
        public async Task<ActionResult<User>> PostUserName([FromBody] UserNameModel Model)
        {

            if (!ModelState.IsValid) { return BadRequest(); }
            User User = await this.UserManager.FindByNameAsync(Model.Username);

            if (User != null) { return Ok(User); } else { return BadRequest("User Not Found"); }
        }
        #endregion

        #region Send doamin verification request mail
        /// <summary>
        /// This sends an email to the super admin to verify the domain
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("SendDomainVerificationRequestMail")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> SendDomainVerificationRequestMail([FromBody] SendVerificationRequestDto model)
        {
            try
            {
                model.Subdomain = this.tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                model.LoggedInUserId = User.GetUserId();

                _logger.Information("Method called: SendDomainVerificationRequestMail", model);
                var response = await this.Services_Repo.UserProfileServices.SendVerificationRequest(model);
                return Ok(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.ToString(), "SendDomainVerificationRequestMail: Exception occured", model);
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Check that invite is still valid
        /// <summary>
        /// Check that invite is still valid
        /// </summary>
        /// <param name="companyEmail"></param>
        /// <returns></returns>
        [Route("IsInviteValid")]
        [HttpGet]
        public async Task<IActionResult> IsInviteValid([ValidEmailCheck] string companyEmail)
        {
            try
            {
                if (companyEmail.Contains("@") == false)
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invalid email address",
                        Data = false
                    });
                }

                if (companyEmail.Contains(" "))
                {
                    companyEmail.Replace(" ", "+");
                }

                // Chk if the user is already a member of the company
                var userCompany = await _publicContext.UserCompanies
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == companyEmail.ToLower());
                if (userCompany is not null)
                {
                    //var secDomains = await _publicContext.SecDomains.Where(x => x.TenantId == userCompany.TenantId).Select(d => d.Domain).ToListAsync();
                    //if (!secDomains.Contains(companyEmail.Split("@")[1]))
                    //{
                    //    return BadRequest(new ApiResponse<bool>
                    //    {
                    //        ResponseCode = "400",
                    //        ResponseMessage = $"This email - {companyEmail} has already been onboarded, kindly login. " +
                    //                        $"Note: Same email address cannot be used to onboard to multiple companies",
                    //        Data = false
                    //    });
                    //}
                }

                var invite = await Services_Repo.CompanyUserInviteService.GetInvite(companyEmail);
                if (invite != null)
                {
                    DateTime twentyFourHoursLater = invite.LastUpdate.AddDays(24);
                    if (DateTime.UtcNow > twentyFourHoursLater || invite.Status == "expired")
                    {
                        if (invite.Status != "expired")
                        {
                            await Services_Repo.CompanyUserInviteService.UpdateInvite(new CompanyUserInviteVM
                            {
                                Email = companyEmail,
                                Status = "expired",
                            });
                        }

                        return BadRequest(new ApiResponse<bool>
                        {
                            ResponseCode = "400",
                            ResponseMessage = "Invite expired. Please request to be re-invited to the platform.",
                            Data = false
                        });
                    }
                    else if (invite.Status == "finalized")
                    {
                        return BadRequest(new ApiResponse<bool>
                        {
                            ResponseCode = "400",
                            ResponseMessage = "Invite has already been used. Please login",
                            Data = false
                        });
                    }
                    else if (invite.Status == "Revoked")
                    {
                        return BadRequest(new ApiResponse<bool>
                        {
                            ResponseCode = "400",
                            ResponseMessage = "Invite has been revoked",
                            Data = false
                        });
                    }
                    else
                    {
                        return Ok(new ApiResponse<bool>
                        {
                            ResponseCode = "200",
                            ResponseMessage = "Invite is valid",
                            Data = true
                        });
                    }
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invite not found. Please request to be invited to the platform.",
                        Data = false
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString(), nameof(IsInviteValid), companyEmail);
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }

        }
        #endregion

        #region Check if an account already exist with the phone number or email
        /// <summary>
        /// Check if an account already exist with the phone number or email
        /// </summary>
        /// <param name="email"></param>
        /// <param name="phoneNumber"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CheckIfAccountExist")]
        public async Task<IActionResult> CheckIfAccountExist(string email, string phoneNumber)
        {
            if (!string.IsNullOrEmpty(email))
            {
                var user = await this.UserManager.FindByEmailAsync(email);
                if (user != null)
                {
                    return BadRequest(new ApiResponse<string>
                    {
                        ResponseCode = "204",
                        ResponseMessage = $"An account with email - {email} exist. Please login with your password",
                        Data = user.Id
                    });
                }
            }

            if (!string.IsNullOrEmpty(phoneNumber))
            {
                var userExist = await this.Services_Repo.Userservice.CheckIfUserExistByPhoneNumber(phoneNumber);
                if (userExist)
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "204",
                        ResponseMessage = $"An account with - {phoneNumber} already exist",
                        Data = true
                    });
                }
            }

            return Ok(new ApiResponse<bool>
            {
                ResponseCode = "200",
                ResponseMessage = "No account found",
                Data = false
            });
        }
        #endregion

        #region Initialize Invited User Process
        /// <summary>
        /// Initailize process for invited user
        /// </summary>
        /// <param name="email"></param>
        /// <param name="tenantId"></param>
        /// <param name="application"></param>
        /// <param name="companyEmail"></param>
        /// <param name="subDomain"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("InitializeInvitedUser")]
        public async Task<IActionResult> InitializeInvitedUser([ValidEmailCheck] string email, Guid tenantId, Applications application,
            [ValidEmailCheck] string companyEmail, string subDomain, string roleId)
        {
            _logger.Information("Method called: InitializeInvitedUser");

            // Trigger background job to complete onboarding
            RecurringJob.RemoveIfExists("complete-onbaording");
            RecurringJob.AddOrUpdate("complete-onbaording", () => _tenantBackgroundJobService.CompleteOnboarding(), Cron.Hourly());
            RecurringJob.TriggerJob("complete-onbaording");

            var message = new List<string>();
            if (string.IsNullOrEmpty(companyEmail))
                message.Add("Company emial is required");
            if (string.IsNullOrEmpty(email))
                message.Add("Personal email is required");

            if (message.Any())
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = string.Join(" - ", message),
                    Data = false
                });

            // Get subdomain from httpcontext
            var subdomain = this.tenantSchema.ExtractSubdomainFromRequest(HttpContext);

            // check if user exists
            var User = await this.UserManager.FindByEmailAsync(email);
            if (User == null)
                return NotFound(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "User not found",
                    Data = false
                });

            // Check that the account with this personal email address has not been onboarded to the company
            var userProfile = await Services_Repo.Userservice.GetUserProfileById(User.Id, null);
            if (userProfile != null && userProfile.Email != companyEmail)
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = $"You have already onboarded to this company with a different email address - {userProfile.Email}",
                    Data = false
                });

            // before creating user verify invite information
            var invite = await Services_Repo.CompanyUserInviteService.GetInvite(companyEmail);
            bool isInvited = false;

            if (invite != null)
            {
                DateTime twentyFourHoursLater = invite.LastUpdate.AddDays(24);
                if (DateTime.UtcNow > twentyFourHoursLater || invite.Status == "expired")
                {
                    if (invite.Status != "expired")
                    {
                        await Services_Repo.CompanyUserInviteService.UpdateInvite(new CompanyUserInviteVM
                        {
                            Email = companyEmail,
                            Status = "expired",
                        });
                    }
                    return BadRequest(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invite expired. Please request to be re-invited to the platform.",
                        Data = null
                    });
                }
                else if (invite.Status == "Revoked")
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invite has been revoked",
                        Data = false
                    });
                }
                else
                {
                    isInvited = true;
                }
            }

            if (User != null && isInvited)
            {
                if (!string.IsNullOrEmpty(tenantId.ToString()))
                {
                    // Check if user is already a member of the company on the public schema
                    var userCompany = await Services_Repo.TenantService.GetUserTenant(User.Id);
                    if (userCompany != null && userCompany.Id == tenantId)
                    {
                        // Check if the user already exists in the company
                        var userExistsOnTenantSchema = await Services_Repo.TenantService.UserExisitsOnCompanySchema(User.Id, null);
                        if (!userExistsOnTenantSchema)
                        {
                            // Update user usertype
                            if (!await Services_Repo.Userservice.UpdateUserType(User.Id, UserTypes.CompanyUser))
                            {
                                _logger.Error($"Failed to update user type for user {User.Id}");
                                return BadRequest(new ApiResponse<bool>
                                {
                                    ResponseCode = "500",
                                    ResponseMessage = "Onboarding failed, please try again later",
                                    Data = true
                                });
                            }

                            // Create new subdomain User profile
                            User.Email = companyEmail;
                            if (!await Services_Repo.Userservice.SaveNewUserProfile(User))
                            {
                                _logger.Error($"Failed to create a new user profile for user {User.Id}");
                                return BadRequest(new ApiResponse<bool>
                                {
                                    ResponseCode = "500",
                                    ResponseMessage = "Onboarding failed, please try again later.",
                                    Data = true
                                });
                            }
                        }

                        // Add user to a role
                        var role = "";
                        if (string.IsNullOrEmpty(roleId))
                            role = "Team Member";
                        else
                            role = await Services_Repo.Userservice.GetUserRoleName(roleId);

                        if (!await Services_Repo.Userservice.AddUserToRole(User.Id, role, application.ToString(), subdomain, _conString))
                        {
                            await DeleteUserProfile(subdomain, User);
                            return BadRequest(new ApiResponse<bool>
                            {
                                ResponseCode = "500",
                                ResponseMessage = "Onboarding failed, please try again later.",
                                Data = true
                            });
                        }
                    }
                    else
                    {
                        // add user to tenant and usercompany relation
                        var res = await Services_Repo.TenantService.AddUserToCompany(User.Id, tenantId, companyEmail);
                        if (res == null)
                        {
                            _logger.Error($"Failed to add user to company - {User.Id}");
                            return BadRequest(new ApiResponse<bool>
                            {
                                ResponseCode = "500",
                                ResponseMessage = "Onboarding failed, please try again later.",
                                Data = true
                            });
                        }

                        // Update user usertype
                        if (!await Services_Repo.Userservice.UpdateUserType(User.Id, UserTypes.CompanyUser))
                        {
                            _logger.Error($"Failed to update user type for user {User.Id}");
                            return BadRequest(new ApiResponse<bool>
                            {
                                ResponseCode = "500",
                                ResponseMessage = "Onboarding failed, please try again later.",
                                Data = true
                            });
                        }

                        // Create new subdomain User profile
                        User.Email = companyEmail;
                        if (!await Services_Repo.Userservice.SaveNewUserProfile(User) || res == null)
                        {
                            _logger.Error($"Failed to create a new user profile for user {User.Id}");
                            return BadRequest(new ApiResponse<bool>
                            {
                                ResponseCode = "500",
                                ResponseMessage = "Onboarding failed, please try again later.",
                                Data = true
                            });
                        }

                        // Add user to a role
                        var role = "";
                        if (string.IsNullOrEmpty(roleId))
                            role = "Team Member";
                        else
                            role = await Services_Repo.Userservice.GetUserRoleName(roleId);

                        if (!await Services_Repo.Userservice.AddUserToRole(User.Id, role, application.ToString(), subdomain, _conString))
                        {
                            await DeleteUserProfile(subdomain, User);
                            _logger.Error($"Failed to add user to role {role} for user {User.Id}");
                            return BadRequest(new ApiResponse<bool>
                            {
                                ResponseCode = "500",
                                ResponseMessage = "Onboarding failed, please try again later.",
                                Data = true
                            });
                        }

                        // Check if user already has access to the application
                        var userAppPermission = await Services_Repo.TenantService.GetUserAppPermission(User.Id, application.ToString(), tenantId.ToString());
                        if (userAppPermission is not null)
                        {
                            return BadRequest(new ApiResponse<bool>
                            {
                                ResponseCode = "204",
                                ResponseMessage = $"User is already a member of the company and already has access to {application}.",
                                Data = true
                            });
                        }
                    }

                    try
                    {
                        // Add app permission for the user
                        var appPermission = await Services_Repo.TenantService.AddUserToAppPermission(User.Id, application.ToString(), null, tenantId.ToString());
                        if (!appPermission)
                        {
                            _logger.Error($"Failed to add user to app permission for user {User.Id}");
                            await DeleteUserProfile(subdomain, User);

                            return BadRequest(new ApiResponse<bool>
                            {
                                ResponseCode = "500",
                                ResponseMessage = "Onboarding failed, please try again later.",
                                Data = true
                            });
                        }
                    }
                    catch (InvalidOperationException ex)
                    {
                        return BadRequest(new ApiResponse<bool>
                        {
                            ResponseCode = "400",
                            ResponseMessage = ex.Message,
                            Data = true
                        });
                    }

                    await Services_Repo.CompanyUserInviteService.UpdateInvite(new CompanyUserInviteVM
                    {
                        Email = companyEmail,
                        Status = "finalized",
                    });
                }

                var template = "";
                if (application == Applications.Joble)
                {
                    template = Utils.Extensions.ReadTemplateFromFile("onboarding-success-email", _environment);
                }
                else
                {
                    template = Utils.Extensions.ReadTemplateFromFile("welcome_email", _environment);
                    template = template.Replace("{name}", userProfile.FirstName).Replace("{auth-url}", Utility.Constants.FRONTEND_AUTH_URL);
                }

                // Publish an 'EmployeeCreatedEvent' to rabbimq
                var userVm = User.MapToUserMDVm();
                userVm.TenantId = tenantId.ToString();
                userVm.CompanyEmail = companyEmail;
                userVm.Subdomain = subDomain;

                var eventModel = new PublishModel
                (
                    RabbitMQConstants.UserCreatedEvent,
                    "",
                    ExchangeType.Fanout,
                    userVm
                );

                var eventRes = await _publisherService.GenericPublish(eventModel);
                if (!eventRes)
                    _logger.Error("Employee onboarded but event could not be published");

                var app = application.ToString();
                return Ok(new ApiResponse<bool>
                {
                    ResponseCode = "204",
                    ResponseMessage = $"Your already a member on the JobPro. Access to {app} has been granted. Kindly login into your dashboard",
                    Data = true
                });
            }
            else
            {
                return BadRequest(new ApiResponse<IdentityResult>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invite not found. Please request to be re-invited to the platform.",
                    Data = null
                });
            }
        }
        #endregion

        #region Process Invite
        /// <summary>
        /// Process Invited User
        /// </summary>
        /// <param name="Model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ProcessInvitedUser")]
        public async Task<IActionResult> HandleInvitedUser([FromBody] RegisterModel Model)
        {
            _logger.Information("Method called: HandleInvitedUser");

            if (!string.IsNullOrEmpty(Model.DateOfBirth))
            {
                // Check if the user is 18 years and above
                var age = DateTime.UtcNow.Year - DateTime.Parse(Model.DateOfBirth).Year;
                if (age < 18)
                {
                    return BadRequest(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "You must be 18 years or above to sign up.",
                        Data = null
                    });
                }
            }

            Model.FirstName = Model.FirstName.ToTitleCase();
            Model.LastName = Model.LastName.ToTitleCase();
            if (!string.IsNullOrEmpty(Model.MiddleName))
                Model.MiddleName = Model.MiddleName.ToTitleCase();

            // Get subdomain from httpcontext
            var subdomain = this.tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            var User = await this.UserManager.FindByEmailAsync(Model.Email);

            bool isFromSocialOauth = false;

            if (!string.IsNullOrEmpty(Model.GoogleAuthToken)) isFromSocialOauth = true;
            if (!string.IsNullOrEmpty(Model.MicrosoftAccessToken)) isFromSocialOauth = true;
            if (isFromSocialOauth)
            {
                var socialAuthResponse = await VerifySocialAuth(Model);
                if (socialAuthResponse.ResponseCode != "200")
                {
                    return BadRequest(socialAuthResponse);
                }
            }

            // before creating user verify invite information
            var emaiInvite = await Services_Repo.CompanyUserInviteService.GetInvite(Model.CompanyEmail);
            bool isInvitedEmail = false;

            if (emaiInvite != null)
            {
                // validate invite
                DateTime twentyFourHoursLater = emaiInvite.LastUpdate.AddDays(24);
                if (DateTime.UtcNow > twentyFourHoursLater || emaiInvite.Status == "expired")
                {
                    if (emaiInvite.Status != "expired")
                    {
                        await Services_Repo.CompanyUserInviteService.UpdateInvite(new CompanyUserInviteVM
                        {
                            Email = Model.CompanyEmail,
                            Status = "expired",
                        });
                    }
                    return BadRequest(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invite expired. Please request to be re-invited to the platform.",
                        Data = null
                    });
                }
                else if (emaiInvite.Status == "Revoked")
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invite has been revoked",
                        Data = false
                    });
                }
                else
                {
                    isInvitedEmail = true;
                }
            }

            //// Check if Phone number has been verified
            //var phoneToken = Services_Repo.OPTService.GetToken(Model.PhoneNumber, OTPIdentifierType.Phone, OTPTokenType.UserSignup, "verified");
            //if (phoneToken == null)
            //{
            //    phoneToken = Services_Repo.OPTService.GetToken(Model.PhoneNumber, OTPIdentifierType.Phone, OTPTokenType.UserSignup, "used");
            //    if (phoneToken != null)
            //    {
            //        return BadRequest(new ApiResponse<IdentityResult>
            //        {
            //            ResponseCode = "400",
            //            ResponseMessage = "Phone number already used. please supply a different phone number.",
            //            Data = null
            //        });
            //    }

            //    return BadRequest(new ApiResponse<IdentityResult>
            //    {
            //        ResponseCode = "500",
            //        ResponseMessage = "Failed to onbaord user. Please verify your Phone Number.",
            //        Data = null,
            //    });
            //}

            // Check if the personal email is verified
            var emailOTP = Services_Repo.OPTService.GetToken(Model.Email, OTPIdentifierType.Email, OTPTokenType.UserSignup, "verified");
            if (emailOTP == null)
            {
                emailOTP = Services_Repo.OPTService.GetToken(Model.Email, OTPIdentifierType.Email, OTPTokenType.UserSignup, "used");
                if (emailOTP != null)
                {
                    return BadRequest(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "400",
                        ResponseMessage = $"Email address - {Model.Email} is already used. please supply a different personal email address.",
                        Data = null
                    });
                }

                return BadRequest(new ApiResponse<IdentityResult>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to onbaord user. Please verify your email personal address.",
                    Data = null,
                });
            }

            if (User == null && isInvitedEmail)
            {
                var jobproId = Utility.RandomString(10);
                var jobproIdExist = await Services_Repo.Userservice.CheckIfJobProIdExist(jobproId);
                while (jobproIdExist)
                {
                    jobproId = Utility.RandomString(10);
                    jobproIdExist = await Services_Repo.Userservice.CheckIfJobProIdExist(jobproId);
                }

                User NewUser = new User
                {
                    JobProId = jobproId,
                    Email = Model.Email,
                    UserName = Model.Email,
                    FirstName = Model.FirstName,
                    LastName = Model.LastName,
                    MiddleName = Model.MiddleName,
                    Gender = Model.Gender,
                    Created_At = DateTime.UtcNow,
                    PhoneNumber = Model.PhoneNumber,
                    Status = "Active",
                    InvitedBy = Model.InvitedBy,
                    PasswordCreatedByAdmin = false,
                    Region = Model.Region.ToString(),
                    CountryCode = Model.CountryCode,
                    Country = Model.Country,
                    UserType = UserTypes.CompanyUser
                };

                IdentityResult Result;
                if (string.IsNullOrEmpty(Model.Password))
                {
                    Result = await UserManager.CreateAsync(NewUser);
                }
                else
                {
                    Result = await UserManager.CreateAsync(NewUser, Model.Password);
                }

                _logger.Information("Create user response " + JsonConvert.SerializeObject(Result));
                if (Result.Succeeded)
                {
                    var user = await this.UserManager.FindByEmailAsync(Model.Email);

                    // Add user to a role
                    var role = "";
                    if (string.IsNullOrEmpty(Model.RoleId))
                        role = "Team Member";
                    else
                        role = await Services_Repo.Userservice.GetUserRoleName(Model.RoleId);

                    if (!await Services_Repo.Userservice.AddUserToRole(user.Id, role, Model.Application.ToString(), subdomain, _conString))
                    {
                        NewUser.Email = Model.Email;
                        await UserManager.DeleteAsync(NewUser);

                        _logger.Error(nameof(HandleInvitedUser), "Failed to add user to a role", Result);
                        return BadRequest(new ApiResponse<bool>
                        {
                            ResponseCode = "500",
                            ResponseMessage = "Onboarding failed, please try again later",
                            Data = true
                        });
                    }

                    if (!string.IsNullOrEmpty(Model.TenantId.ToString()))
                    {
                        var res = await Services_Repo.TenantService.AddUserToCompany(NewUser.Id, Model.TenantId, Model.CompanyEmail);

                        // Create new subdomain User profile
                        var userProfileExist = await Services_Repo.TenantService.UserExisitsOnCompanySchema(user.Id, Model.CompanyEmail);
                        if (!userProfileExist)
                        {
                            NewUser.Email = Model.CompanyEmail;
                            if (!await Services_Repo.Userservice.SaveNewUserProfile(NewUser) || res == null)
                            {
                                // Delete user and role if user profile creation fails
                                await Services_Repo.Userservice.DeleteUserRole(user.Id, role, Model.Application.ToString());
                                NewUser.Email = Model.Email;
                                await UserManager.DeleteAsync(NewUser);

                                _logger.Error("Failed to create user profile", Result);
                                return BadRequest(new ApiResponse<IdentityResult>
                                {
                                    ResponseCode = "500",
                                    ResponseMessage = "Onboarding failed, please try again",
                                    Data = Result
                                });
                            }
                        }
                    }

                    // Add app permission for the user
                    try
                    {
                        var appPermission = await Services_Repo.TenantService.AddUserToAppPermission(user.Id, Model.Application.ToString(), null, Model.TenantId.ToString());
                        if (!appPermission)
                        {
                            _logger.Error($"Failed to add permission for the user {user.Id}");
                        }
                    }
                    catch (InvalidOperationException ex)
                    {
                        _logger.Error(ex.ToString(), nameof(HandleInvitedUser), Model);
                    }

                    // Update invite status
                    var companyInvite = new CompanyUserInviteVM
                    {
                        Email = Model.CompanyEmail,
                        Status = "finalized",
                    };
                    if (!await Services_Repo.CompanyUserInviteService.UpdateInvite(companyInvite))
                    {
                        _logger.Error("Failed to update user's invitation to 'finalised'", companyInvite, nameof(HandleInvitedUser));
                    }

                    //await Services_Repo.OPTService.MarkTokenAsUsed(phoneToken.Id);
                    await Services_Repo.OPTService.MarkTokenAsUsed(emailOTP.Id);

                    // Publish event for employee created
                    var userVm = user.MapToUserMDVm();
                    user.Email = Model.CompanyEmail;
                    userVm.TenantId = Model.TenantId.ToString();
                    userVm.CompanyEmail = Model.CompanyEmail;
                    userVm.Subdomain = subdomain;

                    var eventModel = new PublishModel
                    (
                        RabbitMQConstants.UserCreatedEvent,
                        "",
                        ExchangeType.Fanout,
                        userVm
                    );

                    var eventRes = await _publisherService.GenericPublish(eventModel);
                    if (!eventRes)
                        _logger.Error("Employee onboarded but event could not be published");

                    // Send welcome email
                    var template = "";
                    if (Model.Application == Applications.Joble)
                    {
                        template = Utils.Extensions.ReadTemplateFromFile("onboarding-success-email", _environment);
                    }
                    else
                    {
                        template = Utils.Extensions.ReadTemplateFromFile("welcome_email", _environment);
                        template = template.Replace("{name}", NewUser.FirstName).Replace("{auth-url}", Utility.Constants.FRONTEND_AUTH_URL);
                    }

                    BackgroundJob.Enqueue(() => _emailService.SendEmail(template, NewUser.Email, "Welcome to JobPro"));

                    return Ok(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Onbaording completed successfully",
                        Data = Result,
                        user = user
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Failed to onboard user",
                        Data = Result
                    });
                }
            }
            else
            {
                if (User != null)
                {

                    return BadRequest(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Account with the supplied email already exist on JobPro",
                        Data = null
                    });

                }
                else
                {
                    return BadRequest(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invite not found. Please request to be re-invited to the platform.",
                        Data = null
                    });
                }
            }
        }
        #endregion

        #region User Sign Up
        /// <summary>
        /// Sign up
        /// </summary>
        /// <param name="Model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UserSignup")]
        public async Task<IActionResult> UserSignup([FromBody] UserSignupModel Model)
        {
            if (Model.DateOfBirth is not null)
            {
                // Check if the user is 18 years and above
                var age = DateTime.UtcNow.Year - Model.DateOfBirth.Value.Year;
                if (age < 18)
                {
                    return BadRequest(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "You must be 18 years or above to sign up.",
                        Data = null
                    });
                }
            }

            Model.FirstName = Model.FirstName.ToTitleCase();
            Model.LastName = Model.LastName.ToTitleCase();
            if (!string.IsNullOrEmpty(Model.MiddleName))
                Model.MiddleName = Model.MiddleName.ToTitleCase();

            var User = await this.UserManager.FindByEmailAsync(Model.Email);
            bool isFromGoogleSocialOauth = false;
            bool isFromMicrosoftSocialOauth = false;
            if (User == null)
            {
                User NewUser = null;
                if (!string.IsNullOrEmpty(Model.GoogleAuthToken))
                    isFromGoogleSocialOauth = true;

                if (!string.IsNullOrEmpty(Model.MicrosoftAccessToken))
                    isFromMicrosoftSocialOauth = true;

                // verify user has confirmed their email number
                if (isFromMicrosoftSocialOauth || isFromGoogleSocialOauth)
                {
                    var socialAuthResponse = await VerifySocialAuth(Model);
                    if (socialAuthResponse.ResponseCode != "200")
                        return BadRequest(socialAuthResponse);
                }
                else
                {
                    // Check if email has been verified
                    var token = Services_Repo.OPTService.GetToken(Model.Email, OTPIdentifierType.Email, OTPTokenType.UserSignup, "verified");
                    if (token == null)
                    {
                        return BadRequest(new ApiResponse<IdentityResult>
                        {
                            ResponseCode = "500",
                            ResponseMessage = "User sign-up failed. Please verify email address number.",
                            Data = null,
                        });
                    }
                    else
                    {
                        await Services_Repo.OPTService.MarkTokenAsUsed(token.Id);
                    }

                    // Check if Phone number has been verified
                    var phoneToken = Services_Repo.OPTService.GetToken(Model.PhoneNumber, OTPIdentifierType.Phone, OTPTokenType.UserSignup, "verified");
                    if (phoneToken == null)
                    {
                        return BadRequest(new ApiResponse<IdentityResult>
                        {
                            ResponseCode = "500",
                            ResponseMessage = "User sign-up failed. Please verify your Phone Number.",
                            Data = null,
                        });
                    }
                    else
                        await Services_Repo.OPTService.MarkTokenAsUsed(phoneToken.Id);
                }

                var jobproId = Utility.RandomString(10);
                var jobproIdExist = await Services_Repo.Userservice.CheckIfJobProIdExist(jobproId);
                while (jobproIdExist)
                {
                    jobproId = Utility.RandomString(10);
                    jobproIdExist = await Services_Repo.Userservice.CheckIfJobProIdExist(jobproId);
                }

                NewUser = new User
                {
                    JobProId = jobproId,
                    Email = Model.Email,
                    UserName = Model.Email,
                    FirstName = Model.FirstName,
                    LastName = Model.LastName,
                    MiddleName = Model.MiddleName,
                    Gender = Model.Gender,
                    Country = Model.Country,
                    CountryCode = Model.CountryCode,
                    Created_At = DateTime.UtcNow,
                    PhoneNumber = Model.PhoneNumber,
                    Status = "Active",
                    PasswordCreatedByAdmin = false,
                    IpAddress = Model.IpAddress,
                    Region = Model.Region.ToString(),
                    Profession = Model.Profession,
                    GoogleAuthId = Model.GoogleAuthId,
                    MicrosoftAuthId = Model.MicrosoftAuthId,
                    Address = Model.Address,
                    ZipCode = Model.ZipCode,
                    DateOfBirth = Model.DateOfBirth?.ToShortDateString(),
                    State = Model.State,
                    BaseCurrency = Model.BaseCurrency,
                    ReferralCode = Model.ReferralCode
                };

                IdentityResult Result;
                if (string.IsNullOrEmpty(Model.Password))
                    Result = await UserManager.CreateAsync(NewUser);
                else
                    Result = await UserManager.CreateAsync(NewUser, Model.Password);

                if (Result.Succeeded)
                {
                    // Send welcome email
                    var template = Utils.Extensions.ReadTemplateFromFile("welcome_email", _environment);
                    template = template.Replace("{name}", NewUser.FirstName).Replace("{auth-url}", Utility.Constants.FRONTEND_AUTH_URL);
                    BackgroundJob.Enqueue(() => _emailService.SendEmail(template, NewUser.Email, "Welcome to JobPro"));

                    var user = await this.UserManager.FindByEmailAsync(Model.Email);

                    // Publish an 'IndividualUserCreated Event' to rabbimq
                    var userVm = user.MapToUserMDVm();
                    var eventModel = new PublishModel
                    (
                        RabbitMQConstants.UserCreatedEvent,
                        "",
                        ExchangeType.Fanout,
                        userVm
                    );

                    var eventRes = await _publisherService.GenericPublish(eventModel);
                    if (!eventRes)
                    {
                        _logger.Error("Individual user onboarded but event could not be published");
                        WatchLogger.Log("Individual user onboarded but event could not be published");
                    }

                    return Ok(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Successful",
                        Data = Result,
                        user = user
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Sign-up failed",
                        Data = Result
                    });
                }
            }
            else
            {
                if (User.IndividualUserAccountStatus == IndividualUserAccountStatus.Deleted)
                {
                    return Ok(new ApiResponse<IdentityResult>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Successful",
                        Data = null,
                        user = User
                    });
                }

                return BadRequest(new ApiResponse<IdentityResult>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Email already exist",
                    Data = null
                });
            }
        }
        #endregion

        #region Initialize Email Verification User Signup
        /// <summary>
        /// Initialize Email Verification User Signup
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [Route("InitializeEmailVerificationUserSignup/")]
        public async Task<IActionResult> InitializeEmailVerificationUserSignup(string email)
        {
            try
            {
                var result = await this.Services_Repo.OPTService.RegisterNewUserEmailToken(new NewOTPVM
                {
                    Identifier = email,
                    IdentifierType = OTPIdentifierType.Email,
                    TokenType = OTPTokenType.UserSignup,

                });
                if (result)
                {
                    return Ok(new ApiResponse<bool>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Token generated and sent.",
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "500",
                        ResponseMessage = "Token failed to be generated. Please try again.",
                    });
                }
            }
            catch (RecordAlreadyExistException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                });
            }
        }
        #endregion

        #region Add user to Company
        /// <summary>
        /// Add user to Company
        /// </summary>
        /// <param name="companyEmail"></param>
        /// <param name="role"></param>
        /// <param name="CompanyID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddUserToCompany")]
        public async Task<ActionResult> AddUserToCompany(string companyEmail, string role, string CompanyID)
        {
            if (companyEmail == null) { return BadRequest("User email Is Required!"); }

            Guid tenantId = new Guid(CompanyID);
            var Result = await this.UserManager.FindByEmailAsync(companyEmail);
            var CoyResult = await Services_Repo.TenantService.GetTenantById(tenantId);

            if (Result == null) { return BadRequest(" User Not Found !"); }
            if (CoyResult == null) { return BadRequest("Company Not Found !"); }
            else
            {
                var res = await Services_Repo.TenantService.AddUserToCompany(Result.Id, tenantId, companyEmail);

                // Create new subdomain User profile
                if (!await Services_Repo.Userservice.SaveNewUserProfile(Result) || res == null)
                {
                    return BadRequest(new ApiResponse
                    {
                        ResponseCode = "500",
                        ResponseMessage = "Failed to create a new user profile."
                    });
                }
                Result.CompanyId = CompanyID;
                Result.UserInCompanyRole = role;
                var Result3 = await this.UserManager.UpdateAsync(Result);

                return Ok("Update Successful");
            }
        }
        #endregion

        #region Update user role
        /// <summary>
        /// Update user role
        /// </summary>
        /// <param name="id"></param>
        /// <param name="Model"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateUserRole/{id}")]
        public async Task<ActionResult> UpdateUserWithRolePut(string id, [FromBody] UserRole Model)
        {
            if (id == null) { return BadRequest("Id is Empty"); }

            if (ModelState.IsValid)
            {
                var User = await this.UserManager.FindByIdAsync(id);
                if (User == null) { return NotFound("User Not Found"); }

                var CurrentRole = await this.UserManager.GetRolesAsync(User);

                var RemoveResult = await this.UserManager.RemoveFromRoleAsync(User, CurrentRole.ToString());

                if (RemoveResult.Succeeded)
                {

                    var AddResult = await UserManager.AddToRoleAsync(User, Model.RoleName);


                    if (AddResult.Succeeded) { return Ok("User Role Updated Successfully"); }

                }
            }

            return BadRequest();
        }
        #endregion

        #region Delete User
        /// <summary>
        /// Delete User
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("Delete/{id}")]
        public async Task<ActionResult> DeleteUser(string id)
        {
            if (id == null) { return NotFound("Id is Empty"); }

            User User = await this.UserManager.FindByIdAsync(id);

            if (User != null)
            {

                var Result = await this.UserManager.DeleteAsync(User);

                if (Result.Succeeded) { return Ok("User Deleted"); } else { return BadRequest(); }
            }
            else { return BadRequest(); }
        }
        #endregion

        #region Activate/Deactivate or Delete an Individual User Account
        /// <summary>
        /// Activate/Deactivate or Delete an Individual User Account
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("ActivateDeactivateOrDeleteIndUserAcct")]
        public async Task<IActionResult> ActivateDeactivateOrDeleteIndUserAcct(string userId, IndividualUserAccountStatus status)
        {
            try
            {
                var response = await Services_Repo.Userservice.ActivateDeactivateOrDeleteIndUserAcct(userId, status);
                return response.ResponseCode == "200" ? Ok(response) : StatusCode(Convert.ToInt32(response.ResponseCode), response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error($"Exception:ActivateDeactivateOrDeleteIndUserAcct {ex.Message}");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Operation failed, please try again later",
                    DevResponseMessage = ex.Message
                });
            }
        }
        #endregion

        #region Reset lockout
        [HttpPost("UnlockUser/{userId}")]
        public async Task<IActionResult> UnlockLockedUser([FromRoute] string userId)
        {
            var user = await UserManager.FindByIdAsync(userId);
            if (user == null)
            {
                return NotFound($"User with ID '{userId}' not found.");
            }

            // Remove lockout
            var result = await UserManager.SetLockoutEndDateAsync(user, DateTimeOffset.UtcNow);

            if (!result.Succeeded)
            {
                return StatusCode(500, "Failed to unlock the user.");
            }

            // Optional: reset failed login count
            await UserManager.ResetAccessFailedCountAsync(user);

            return Ok(new
            {
                message = $"User {user.UserName} has been unlocked.",
                userId = user.Id,
                unlockedAt = DateTime.UtcNow
            });
        }
        #endregion

        #region Post Login
        [Authorize]
        [HttpPost]
        [Route("PostLogin")]
        public async Task<ActionResult> PostLogin(Applications app, string browserId = null)
        {
            var userId = User.GetUserId();

            var subDomain = tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            var rootDomain = _appSettings.RootDomain;
            var user = await this.UserManager.FindByIdAsync(userId);
            UserProfile userProfile = null;
            TwoFactorSetting company2fa = null;
            bool userWikiAccess = false;

            if (!string.IsNullOrEmpty(browserId))
            {
                // Get browser info and add to the database
                var browserDetection = new BrowserDetection(HttpContext.Request);
                var browserInfo = browserDetection.GetBrowserInfo();
                if (!browserInfo.IsMobile)
                {
                    _logger.Error("Invalid request. Must be from any of the jobpro mobile appplications.");
                    return BadRequest("Invalid request. Must be from any of the jobpro mobile apps.");
                }

                if (browserId.Length != 40)
                {
                    _logger.Error("Invalid browser ID. Must be 40 characters.");
                    return BadRequest("Invalid browser ID. Must be 40 characters.");
                }

                // Check that the browserId is valid
                var browser = await _publicContext.BrowserInfos.FirstOrDefaultAsync(x => x.BrowserId == browserId);
                if (browser == null)
                {
                    _logger.Error("Invalid browser ID. Browser ID not found.");
                    return BadRequest("Invalid browser ID. Browser ID not found.");
                }
            }

            // find tenant and company user belongs to and then get the apps the user has access to
            var userCompany = Services_Repo.UserCompaniesServices.GetUserCompany(userId, subDomain);

            var userPermittedApp = new List<string>();
            Localization localization = null;
            if (userCompany != null)
            {
                await using var context = new JobProDbContext(_conString, new DbContextSchema(userCompany.tenant.Subdomain));

                var company2faRes = await Services_Repo.AdminService.GetTwoFactorSettings();
                company2fa = company2faRes.Data as TwoFactorSetting;

                // Check if the user is suspended
                var isUserSuspended = await context.SuspendedEmployees.Where(x => x.UserId == userId)
                    .Select(sus => sus.IsSuspended)
                    .FirstOrDefaultAsync();
                if (isUserSuspended)
                    return BadRequest("Your currently suspended");

                userProfile ??= await this.Services_Repo.Userservice.GetUserProfileById(userId, context);
                var userAppPermissions = (await Services_Repo.TenantService.GetUserAppPermissions(userId, false, userCompany.TenantId.ToString(), context));
                if (userAppPermissions.Any())
                {
                    userPermittedApp = userAppPermissions.Select(x => x.Application).ToList();
                }

                if (app != Applications.JobID && app != Applications.JobPays)
                {
                    if (userPermittedApp.Any())
                    {
                        if (!userPermittedApp.Contains(app.ToString()))
                        {
                            return BadRequest("Post Login failed. User does not have access to this application");
                        }
                    }
                }

                // Get user's localization details, if the user has not set up their localization details, use the company localization details. If the company has not set up their localization details, defaults to none
                var userLocalization = await Services_Repo.AdminService.GetLocalization(userId);
                if (userLocalization == null)
                {
                    userLocalization = await Services_Repo.AdminService.GetLocalization();
                    if (userLocalization == null)
                    {
                        localization = null;
                    }
                    else
                    {
                        localization = userLocalization.Data as Localization;
                    }
                }

                // validate user has access to this schema
                if (userCompany.tenant.Subdomain != tenantSchema.ExtractSubdomainFromRequest(HttpContext) && rootDomain != subDomain && subDomain != "api")
                {
                    return BadRequest("Invalid token. User does not exit on this company");
                }

                // Get User wiki access
                userWikiAccess = await Services_Repo.WikiAccess.HasAccessToWiki(userId);
            }
            else
            {
                // Check if the user's individual account is deleted or inactive
                if (user.IndividualUserAccountStatus == IndividualUserAccountStatus.InActive)
                    return BadRequest("Your individual account is currently inactive. Re-activate to be able to use your individual account.");
                if (user.IndividualUserAccountStatus == IndividualUserAccountStatus.Deleted)
                    return BadRequest("Login failed. Account no longer exists.");

                var userAppPermissions = await Services_Repo.TenantService.GetUserAppPermissions(userId, false, null, null);
                if (userAppPermissions.Any())
                {
                    userPermittedApp = userAppPermissions.Select(x => x.Application).ToList();
                }
            }

            if (string.Equals("Blocked", user.Status, StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest("User has been blocked");
            }
            else
            {
                var Role = await _adminService.GetUserRole(user.Id, userCompany?.tenant?.Subdomain);
                var userPermissions = await _adminService.GetUserPermissions(user.Id, userCompany?.tenant?.Subdomain);

                var GeneratedRefreshToken = await this.GenerateRefreshToken(this.ipAddress());
                var RefreshToken = GeneratedRefreshToken.Token;
                await Services_Repo.Userservice.AddOrUpdateRefreshToken(GeneratedRefreshToken);

                var userDetails = _mapper.Map<UserDetails>(user);
                if (!string.IsNullOrEmpty(browserId))
                {
                    await _hubContext.Clients.Group(browserId).Login(new
                    {
                        user = userDetails,
                        user.Id,
                        user.UserName,
                        user.Email,
                        user.UserType,
                        Role,
                        userPermissions,
                        userCompany,
                        userProfile,
                        userPermittedApp,
                        RefreshToken,
                        localization,
                        CompanyTwoFactorSettings = company2fa,
                        HasAccessToWiki = userWikiAccess
                    });

                    return Ok("Smart login was successful");
                }

                return Ok(new
                {
                    user = userDetails,
                    user.Id,
                    user.UserName,
                    user.Email,
                    user.UserType,
                    Role,
                    userPermissions,
                    userCompany,
                    userProfile,
                    userPermittedApp,
                    RefreshToken,
                    CompanyTwoFactorSettings = company2fa,
                    HasAccessToWiki = userWikiAccess
                });
            }
        }
        #endregion

        #region Generate QR Code for smart login
        /// <summary>
        /// Generate QR Code for smart login
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GenerateQRCode")]
        public async Task<ActionResult> GenerateQRCode([Required] string id)
        {
            var httpRequest = HttpContext.Request;
            var qrCode = await Services_Repo.Userservice.GenerateQRCodeForSmartLogin(id, httpRequest);
            return Ok(qrCode);
        }
        #endregion

        #region Login
        /// <summary>
        /// User Login
        /// </summary>
        /// <param name="Model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AuthenticateUser")]
        public async Task<ActionResult> PostUser([FromBody] AuthenticationModel Model)
        {
            var LoggedInWithEmail = Model.Email;

            try
            {
                IPAddress remoteIpAddress = Request.HttpContext.Connection.RemoteIpAddress;
                if (!ModelState.IsValid) { return BadRequest(); }

                var subDomain = tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var rootDomain = _appSettings.RootDomain;
                var User = await this.UserManager.FindByEmailAsync(Model.Email);
                UserProfile userProfile = null;
                TwoFactorSetting company2fa = null;
                string message = null;
                bool twofaOptionUpdated = false;

                if (User == null)
                {
                    userProfile = await Services_Repo.Userservice.GetUserByEmail(Model.Email);
                    if (userProfile != null)
                        User = await this.UserManager.FindByIdAsync(userProfile.UserId);
                }

                if (userProfile == null)
                    userProfile = await Services_Repo.Userservice.GetUserByEmail(Model.Email);

                if (User == null)
                {
                    return NotFound("Invalid Email or Password");
                }

                if (Model.Application == Applications.JobPays)
                {
                    if (!Model.FromMobile && User.UserType.ToString() != "CompanyAdmin")
                        return BadRequest("Please use the mobile app to login");
                }

                // Check user's current system ipAddress is locked on Jobpro
                var ipAdress = this.Services_Repo.Userservice.GetIp(remoteIpAddress);
                if (User.IpAddress == ipAdress && User.lockIpAddress)
                    return BadRequest("Your current system IP address is locked, please contact your administrator");
                if (User.lockIpAddress)
                    User.lockIpAddress = false;

                // find tenant and company user belongs to and then get the apps the user has access to
                var userCompany = Services_Repo.UserCompaniesServices.GetUserCompany(User.Id, subDomain);

                var userPermittedApp = new List<string>();
                Localization localization = null;
                if (userCompany != null)
                {
                    if (User.Id != null)
                    {
                        await using var context = new JobProDbContext(_conString, new DbContextSchema(userCompany.tenant.Subdomain));

                        // Check if the user is suspended
                        var isUserSuspended = await context.SuspendedEmployees.Where(x => x.UserId == User.Id)
                            .Select(sus => sus.IsSuspended)
                            .FirstOrDefaultAsync();
                        if (isUserSuspended)
                            return BadRequest("Your currently suspended");

                        userProfile ??= await this.Services_Repo.Userservice.GetUserProfileById(User.Id, context);
                        if (userProfile == null)
                            return BadRequest("Login failed, please try again later.");

                        var userAppPermissions = (await Services_Repo.TenantService.GetUserAppPermissions(User.Id, false, userCompany.TenantId.ToString(), context));
                        if (userAppPermissions.Any())
                        {
                            userPermittedApp = userAppPermissions.Select(x => x.Application).ToList();
                        }
                    }

                    if (Model.Application != Applications.JobID && Model.Application != Applications.JobPays && Model.Application != Applications.JobFy)
                    {
                        if (userPermittedApp.Any() && Model.Application != null)
                        {
                            if (!userPermittedApp.Contains(Model.Application.ToString()))
                            {
                                return BadRequest("Login failed. User does not have access to this application");
                            }
                        }
                    }

                    // Get user's localization details, if the user has not set up their localization details, use the company localization details. If the company has not set up their localization details, defaults to none
                    var userLocalization = await Services_Repo.AdminService.GetLocalization(User.Id);
                    if (userLocalization == null)
                    {
                        userLocalization = await Services_Repo.AdminService.GetLocalization();
                        if (userLocalization == null)
                        {
                            localization = null;
                        }
                        else
                        {
                            localization = userLocalization.Data as Localization;
                        }
                    }

                    // validate user has access to this schema
                    if (userCompany.tenant.Subdomain != tenantSchema.ExtractSubdomainFromRequest(HttpContext) && rootDomain != subDomain && subDomain != "api")
                    {
                        return BadRequest("Invalid email or password. User does not exit on this company");
                    }
                }
                else
                {
                    // Check if the user's individual account is deleted or inactive
                    if (User.IndividualUserAccountStatus == IndividualUserAccountStatus.InActive)
                        return BadRequest("Login failed, your individual account is currently inactive. Re-activate to be able to use your individual account.");
                    if (User.IndividualUserAccountStatus == IndividualUserAccountStatus.Deleted)
                        return BadRequest("Account no longer exists.");

                    var userAppPermissions = await Services_Repo.TenantService.GetUserAppPermissions(User.Id, false, null, null);
                    if (userAppPermissions.Any())
                    {
                        userPermittedApp = userAppPermissions.Select(x => x.Application).ToList();
                    }
                }

                var validateResult = await this.Services_Repo.Userservice.ValidateUserIp(User, ipAdress);
                if (!validateResult)
                {
                    return BadRequest("Invalid Ip Address");
                }

                User.IpAddress = ipAdress;
                await this.UserManager.UpdateAsync(User);

                if (string.Equals("Blocked", User.Status, StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest("User has been blocked");
                }
                else
                {
                    if ((User.PasswordCreatedByAdmin && (DateTime.UtcNow.Second - User.Created_At.Second) < 3600) || !User.PasswordCreatedByAdmin)
                    {
                        var Role = await _adminService.GetUserRole(User.Id, userCompany?.tenant?.Subdomain);
                        var userPermissions = await _adminService.GetUserPermissions(User.Id, userCompany?.tenant?.Subdomain);
                        var Result = await this.SignInManager.PasswordSignInAsync(User.UserName, Model.Password, Model.RememberMe, lockoutOnFailure: true);

                        if (Result.IsLockedOut)
                        {
                            int lockedOutForHowLongInMinutes = 10;
                            var lockedOutDateAndTime = User.LockoutEnd.Value.AddMinutes(-lockedOutForHowLongInMinutes);
                            var minutes = (int)lockedOutDateAndTime.Minute - DateTime.UtcNow.Minute;

                            // Send account locked email notification
                            string templatePath = Path.Combine(_webHostEnvironment.WebRootPath, "EmailTemplates", "auth", "account_locked.html");
                            string emailTemplate = System.IO.File.ReadAllText(templatePath);

                            // Replace placeholders in the template
                            string userName = $"{User.FirstName} {User.LastName}";
                            emailTemplate = emailTemplate.Replace("[User_Name]", userName);
                            emailTemplate = emailTemplate.Replace("[No_Of_Incorrect_Attempt]", "3");

                            var passwordResetLink = $"{_appSettings.FrontendUrl}/auth/reset-password";
                            emailTemplate = emailTemplate.Replace("[Password_Reset_Link]", passwordResetLink);

                            // Send the email notification using the email service
                            BackgroundJob.Enqueue(() => _emailService.SendEmail(emailTemplate, User.Email, "Account Locked - Security Alert"));

                            if (minutes > 0)
                            {
                                return BadRequest($"Your account has been temporarily locked for {minutes} minutes. If your sure of your password, you can retry in {minutes} minutes or reset your password and try again in {minutes} minutes");
                            }

                            return BadRequest("Your account is temporarily locked, please try again in few minutes or contact support");
                        }

                        if (Result.Succeeded)
                        {
                            // If its a company user, chkeck if 2fa is enabled first on a company level and then on a user level
                            if (userCompany != null)
                            {
                                var company2faRes = await Services_Repo.AdminService.GetTwoFactorSettings();
                                company2fa = company2faRes.Data as TwoFactorSetting;
                                if (company2fa != null && company2fa.options != AdminConsole.Enums._2FAOptions.None)
                                {
                                    if (company2fa.ImidiatelyRequire2FA)
                                    {
                                        if (!userProfile.IsTwoFactorEnabled)
                                        {
                                            message = "Two factor authentication is required by your company. Please set it up and enable it to continue.";
                                            return BadRequest(message);
                                        }

                                        if (userProfile.IsTwoFactorEnabled && company2fa.options != AdminConsole.Enums._2FAOptions.All && !company2fa.options.ToString().Contains(userProfile._2FAOptions.ToString()))
                                        {
                                            twofaOptionUpdated = true;
                                            message = "Your company’s 2FA settings have been updated. Please review and update your authentication settings accordingly.";
                                        }
                                    }                                   
                                }

                                if (userProfile.IsTwoFactorEnabled && userProfile._2FAOptions.ToString().Contains(AdminConsole.Enums._2FAOptions.AuthenticatorApp.ToString()) && string.IsNullOrEmpty(Model.TempToken))
                                {
                                    var tempToken = await GenerateTempJwtTokenAsync(User.Id);
                                    if (tempToken == null)
                                        return BadRequest("Failed to generate token. Please try again.");

                                    var tempLoginRes = new LoginResponseDto
                                    {
                                        TempToken = tempToken,
                                        Message = userProfile._2FAOptions == AdminConsole.Enums._2FAOptions.AuthenticatorApp ? "Two factor authentication is required. Please enter the code from your authenticator app" : "Two factor authentication is required. Please enter the code sent to your phone number.",
                                    };

                                    return Ok(tempLoginRes);
                                }
                                else if (!string.IsNullOrEmpty(Model.TempToken))
                                {
                                    if (string.IsNullOrEmpty(Model.Code))
                                        return BadRequest("Please enter the code sent to your phone number or authenticator app.");

                                    // Validate the token
                                    var userId = ValidateTempJwtTokenAndExtractUserId(Model.TempToken);
                                    if (userId == null)
                                        return BadRequest("Invalid token.");
                                    if (userId != User.Id)
                                        return BadRequest("Invalid token. User does not exist on this company.");

                                    var verificationRes = await Services_Repo.I2FAService.Verify2FA(User.Id, Model.Code);
                                    if (!verificationRes)
                                    {
                                        return BadRequest("Invalid code. Please try again.");
                                    }
                                }
                            }

                            var AccessToken = await this.GenerateJwtTokenAsync(Model.Email, User, Role, string.IsNullOrEmpty(User.CompanyId) ? userCompany?.tenant?.Id.ToString() : User.CompanyId, userCompany?.tenant?.Subdomain);
                            var GeneratedRefreshToken = await this.GenerateRefreshToken(this.ipAddress());

                            var RefreshToken = GeneratedRefreshToken.Token;
                            GeneratedRefreshToken.UserId = User.Id;
                            await Services_Repo.Userservice.AddOrUpdateRefreshToken(GeneratedRefreshToken);

                            this.setTokenCookie(RefreshToken);
                            
                            // Check if user is logging in from a new IP address and send notification if needed
                            await CheckAndSendNewDeviceLoginNotification(User, ipAdress);

                            ClientRole userRole = null;

                            if (!string.IsNullOrEmpty(User.ClientRoleId.ToString()))
                            {
                                userRole = await this.Services_Repo.ClientService.GetRoleById(User.ClientRoleId.ToString());
                            }

                            var newRole = await this.Services_Repo.ClientService.GetCLientRole(userRole);

                            // Log login activity
                            await Services_Repo.Userservice.LogLastLogin(User.Id);

                            var userDetails = _mapper.Map<UserDetails>(User);
                            var UserType = userCompany != null ? User.UserType : UserTypes.IndividualUser;

                            // Get User wiki access
                            var userWikiAccess = await Services_Repo.WikiAccess.HasAccessToWiki(User.Id, Role == "Super Admin");

                            // Get a presigned link for profile is is not null
                            var loginRes = new LoginResponseDto
                            {
                                User = userDetails,
                                Id = User.Id,
                                UserName = User.UserName,
                                Email = User.Email,
                                UserType = UserType,
                                AccessToken = AccessToken,
                                RefreshToken = RefreshToken,
                                Role = Role,
                                userPermissions = userPermissions,
                                newRole = newRole,
                                userCompany = userCompany,
                                userProfile = userProfile,
                                userPermittedApp = userPermittedApp,
                                LoggedInWithEmail = LoggedInWithEmail,
                                localization = localization,
                                Message = message ?? "Login was successful",
                                CompanyTwoFactorSettings = company2fa,
                                TwoFaOptionsUpdated = twofaOptionUpdated,
                                HasAccessToWiki = userWikiAccess
                            };

                            if (AccessToken != null)
                            {
                                return Ok(loginRes);
                            }

                            else { return BadRequest("Token Error"); }
                        }
                        else { return BadRequest("Invalid login attempt - wrong username or password"); }
                    }
                    else { return BadRequest("Password expired, please reset."); }
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse
                {
                    DevResponseMessage = ex.ToString(),
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while processing your request"
                });
            }
        }
        #endregion

        #region Internal Service Login
        /// <summary>
        /// Internal Service Login
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("InternalServiceLogin")]
        public async Task<IActionResult> InternalServiceLogin(GrpcAuthenticateRequest req)
        {
            var response = await Services_Repo.Userservice.InternalServiceLogin(req);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region PayrollVendor Login
        /// <summary>
        /// PayrollVendor Login
        /// </summary>
        /// <param name="Model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AuthenticatePayrollVendor")]
        public async Task<ActionResult> AuthenticatePayrollVendor([FromBody] AuthenticationModel Model)
        {
            IPAddress remoteIpAddress = Request.HttpContext.Connection.RemoteIpAddress;
            if (!ModelState.IsValid) { return BadRequest(); }

            try
            {
                var rootDomain = _appSettings.RootDomain;
                var User = await this.UserManager.FindByEmailAsync(Model.Email);
                UserProfile userProfile = null;
                if (User == null)
                {
                    userProfile = await this.Services_Repo.Userservice.GetUserByEmail(Model.Email);
                    if (userProfile != null)
                        User = await this.UserManager.FindByIdAsync(userProfile.UserId);
                }

                if (User == null) { return NotFound("Invalid Email or Password"); }
                if (Model.Application == Applications.JobPays)
                {
                    if (!Model.FromMobile && User.UserType.ToString() != "CompanyAdmin")
                        return BadRequest("Please use the mobile app to login");
                }

                // Check user's current system ipAddress is locked on Jobpro
                var ipAdress = this.Services_Repo.Userservice.GetIp(remoteIpAddress);
                if (User.IpAddress == ipAdress && User.lockIpAddress)
                    return BadRequest("Your current system IP address is locked, please contact your administrator");
                if (User.lockIpAddress)
                    User.lockIpAddress = false;

                // find tenant and company user belongs to and then get the apps the user has access to
                var userCompany = Services_Repo.UserCompaniesServices.GetUserCompany(User.Id);

                // Get the last visited company
                var lastVisitedCompany = await Services_Repo.UserCompaniesServices.GetDefaultCompany(User.Id);
                if (lastVisitedCompany is not null)
                    userCompany.tenant.Subdomain = lastVisitedCompany;

                var userPermittedApp = new List<string>();
                if (userCompany != null)
                {
                    if (User.Id != null)
                    {
                        await using var context = new JobProDbContext(_conString, new DbContextSchema(userCompany.tenant.Subdomain));
                        userProfile ??= await this.Services_Repo.Userservice.GetUserProfileById(User.Id, context);
                        var userAppPermissions = (await Services_Repo.TenantService.GetUserAppPermissions(User.Id, false, userCompany.TenantId.ToString(), context));
                        if (userAppPermissions.Any())
                        {
                            userPermittedApp = userAppPermissions.Select(x => x.Application).ToList();
                        }
                    }

                    if (userPermittedApp.Any() && Model.Application != null)
                    {
                        if (!userPermittedApp.Contains(Model.Application.ToString()))
                        {
                            return BadRequest("Login failed. User does not have access to this application");
                        }
                    }

                    // validate user has access to this schema
                    var subDomain = tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                    if (userCompany.tenant.Subdomain != tenantSchema.ExtractSubdomainFromRequest(HttpContext) && rootDomain != subDomain && subDomain != "api")
                    {
                        return BadRequest("Invalid email or password. User does not exit on this company");
                    }
                }
                else
                {
                    var userAppPermissions = await Services_Repo.TenantService.GetUserAppPermissions(User.Id, false, null, null);
                    if (userAppPermissions.Any())
                    {
                        userPermittedApp = userAppPermissions.Select(x => x.Application).ToList();
                    }
                }

                // if user login is valid, verify user KYC status
                var validateResult = await this.Services_Repo.Userservice.ValidateUserIp(User, ipAdress);
                if (!validateResult)
                {
                    return BadRequest("Invalid Ip Address");
                }

                User.IpAddress = ipAdress;
                await this.UserManager.UpdateAsync(User);

                if (string.Equals("Blocked", User.Status, StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest("User has been blocked");
                }
                else
                {
                    if ((User.PasswordCreatedByAdmin && (DateTime.UtcNow.Second - User.Created_At.Second) < 3600) || !User.PasswordCreatedByAdmin)
                    {
                        string Role = null; //await _adminService.GetUserRole(User.Id, userCompany.tenant.Subdomain);
                        //var userPermissions = await _adminService.GetUserPermissions(User.Id, userCompany.tenant.Subdomain);
                        var Result = await this.SignInManager.PasswordSignInAsync(User.UserName, Model.Password, Model.RememberMe, lockoutOnFailure: true);

                        if (Result.Succeeded)
                        {
                            var AccessToken = await this.GenerateJwtTokenAsync(Model.Email, User, Role);
                            var GeneratedRefreshToken = await this.GenerateRefreshToken(this.ipAddress());

                            var RefreshToken = GeneratedRefreshToken.Token;
                            GeneratedRefreshToken.UserId = User.Id;
                            await Services_Repo.Userservice.AddOrUpdateRefreshToken(GeneratedRefreshToken);

                            this.setTokenCookie(RefreshToken);

                            ClientRole userRole = null;

                            if (!string.IsNullOrEmpty(User.ClientRoleId.ToString()))
                            {
                                userRole = await this.Services_Repo.ClientService.GetRoleById(User.ClientRoleId.ToString());
                            }

                            var newRole = await this.Services_Repo.ClientService.GetCLientRole(userRole);

                            if (AccessToken != null)
                            {
                                return Ok(new
                                {
                                    User,
                                    User.Id,
                                    User.UserName,
                                    User.Email,
                                    User.UserType,
                                    AccessToken,
                                    RefreshToken,
                                    Role,
                                    //userPermissions,
                                    newRole,
                                    userCompany,
                                    userProfile,
                                    userPermittedApp,
                                });
                            }

                            else { return BadRequest("Token Error"); }
                        }
                        else { return BadRequest("Invalid login attempt - wrong username or password"); }
                    }
                    else { return BadRequest("Password expired, please reset."); }
                }
            }

            catch (Exception Ex)
            { return BadRequest("Model State Error" + Ex); }
        }
        #endregion

        #region Get App Permission of a user
        /// <summary>
        /// Get app permissions
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="tenantId"></param>
        /// <param name="getFavorites"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserAppPermissions")]
        public async Task<IActionResult> GetUserAppPermissions(string userId, bool getFavorites, string tenantId = null)
        {
            // Get the applications the user has access to
            var userPermittedApp = new List<string>();

            if (userId != null)
            {
                var userAppPermissions = (await Services_Repo.TenantService.GetUserAppPermissions(userId, false, tenantId, null));
                if (userAppPermissions != null)
                {
                    userPermittedApp = userAppPermissions.Select(x => x.Application).ToList();
                }
            }

            return Ok(new ApiResponse<List<string>>
            {
                ResponseCode = "200",
                ResponseMessage = "Permissions retrived successfully",
                Data = userPermittedApp
            });
        }
        #endregion

        #region Log Out
        /// <summary>
        /// Log out a user
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("LogOut")]
        public async Task<ActionResult> PostLogOut()
        {
            await this.SignInManager.SignOutAsync();
            var response = await this.Services_Repo.Userservice.RevokeToken(ipAddress());
            if (!response)
                _logger.Error("Logged out successfully but RefreshToken could not be revoked");

            var jwtToken = GetJwtTokenFromRequest();  // Method to extract the JWT from the request headers
            var blacklistResponse = await this.Services_Repo.Userservice.BlacklistJwtToken(jwtToken);
            if (!blacklistResponse)
            {
                _logger.Error("Logged out, but the JWT token could not be invalidated");
            }

            return Ok("Signed out successfully");
        }
        #endregion

        #region Initiate Forget Password
        /// <summary>
        /// Forgoet Password Verification Request
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("InitiatePasswordReset")]
        public async Task<ApiResponse<IdentityResult>> InitiatePasswordReset(string email)
        {
            email = email.ToLower();
            if (!email.IsValidEmail())
                return new ApiResponse<IdentityResult> { Data = null, ResponseCode = "400", ResponseMessage = "Email is not valid!" };

            var user = this.UserManager.FindByEmailAsync(email).Result;
            if (user == null)
            {
                // Get userId using the email/company email from UserCompanies table
                var userId = await _publicContext.UserCompanies.Where(u => u.Email.ToLower() == email)
                    .Select(x => x.UserId).FirstOrDefaultAsync();
                if (!string.IsNullOrEmpty(userId))
                    user = this.UserManager.FindByIdAsync(userId).Result;
                if (user == null)
                {
                    return new ApiResponse<IdentityResult> { Data = null, ResponseCode = "400", ResponseMessage = "No account found for the supplied email" };
                }
            }

            var token = await UserManager.GeneratePasswordResetTokenAsync(user);
            string encodedToken = HttpUtility.UrlEncode(token);
            var url = string.Format(Utility.Constants.FRONTEND_RESET_PASSWORD + "?token={0}&email={1}", encodedToken, user.Email);

            var template = System.IO.File.ReadAllText(Path.Combine(_environment.WebRootPath, @"EmailTemplates/reset-password.html"));
            template = template.Replace("{name}", user.FirstName + " " + user.LastName).Replace("{url}", url);
            await _emailService.SendEmail(template, email, "PASSWORD RESET");

            return new ApiResponse<IdentityResult> { Data = null, ResponseCode = "200", ResponseMessage = "Password reset link sent" };
        }
        #endregion

        #region Reset Password
        /// <summary>
        /// Reset a user password
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ResetPassword")]
        public async Task<IActionResult> ResetPassword(ResetPasswordDto model)
        {
            var user = await UserManager.FindByEmailAsync(model.Email.ToLower());
            if (user == null)
            {
                // Get userId using the email/company email from UserCompanies table
                var userId = await _publicContext.UserCompanies.Where(u => u.Email.ToLower() == model.Email.ToLower())
                    .Select(x => x.UserId).FirstOrDefaultAsync();
                if (!string.IsNullOrEmpty(userId))
                    user = this.UserManager.FindByIdAsync(userId).Result;
                if (user == null)
                {
                    return BadRequest(new ApiResponse<IdentityResult>
                    {
                        Data = null,
                        ResponseCode = "400",
                        ResponseMessage = "No account found for the supplied email"
                    });
                }
            }

            IdentityResult result = null;
            try
            {
                result = await UserManager.ResetPasswordAsync(user, model.Token, model.Password);
                if (result.Succeeded)
                {
                    return Ok(new ApiResponse<IdentityResult>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Reset password processed successfully"
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<IdentityResult>
                    {
                        Data = result,
                        ResponseCode = "400",
                        ResponseMessage = "Reset password failed"
                    });
                }

            }
            catch (Exception ex)
            {
                Log.Error("Exception caught in reset password ", ex.Message);
                return BadRequest(new ApiResponse<IdentityResult>
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Verify Password
        [Authorize]
        [HttpPost]
        [Route("VerifyPassword")]
        public async Task<IActionResult> VerifyPassword(VerifyPasswordDto model)
        {
            var user = await UserManager.FindByIdAsync(model.UserId);
            if (user == null)
            {
                return BadRequest(new ApiResponse<IdentityResult>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = "No account found for the supplied email"
                });
            }

            var isCorrect = await UserManager.CheckPasswordAsync(user, model.Password);
            if (isCorrect)
            {
                return Ok(new ApiResponse<IdentityResult>
                {
                    Data = null,
                    ResponseCode = "200",
                    ResponseMessage = "Password verified successfully"
                });
            }
            else
            {
                return BadRequest(new ApiResponse<IdentityResult>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = "Password verification failed"
                });
            }
        }
        #endregion

        #region Change Password
        /// <summary>
        /// Change Password
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPut]
        [Route("ChangePassword")]
        public async Task<IActionResult> ChangePassword(ChangePasswordDto model)
        {
            var userId = User.GetUserId();
            IdentityResult result = null;

            var user = await UserManager.FindByIdAsync(userId);
            if (user == null)
            {
                return BadRequest(new ApiResponse<IdentityResult>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = "User does not exist"
                });
            }

            // Check if the old password is correct
            var isCorrect = await UserManager.CheckPasswordAsync(user, model.CurrentPassword);
            if (!isCorrect)
            {
                return BadRequest(new ApiResponse<IdentityResult>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = "Current password is incorrect"
                });
            }

            // Check if the company has a password policy and if the new password meets the policy requirements
            //var passwordPolicyRes = await _adminService.GetPasswordPolicy();
            //if (passwordPolicyRes.Data != null)
            //{
            //    var passwordPolicies = passwordPolicyRes.Data as PasswordPolicy;
            //    var errorMessage = ValidatePasswordAgainstPolicy(model, user.UserName, passwordPolicies);
            //    if (errorMessage != null)
            //    {
            //        return BadRequest(new ApiResponse<IdentityResult>
            //        {
            //            Data = null,
            //            ResponseCode = "400",
            //            ResponseMessage = errorMessage
            //        });
            //    }
            //}

            // Change password
            result = await UserManager.ChangePasswordAsync(user, model.CurrentPassword, model.NewPassword);
            var response = await this.Services_Repo.Userservice.RevokeToken(ipAddress());
            if (!response)
                _logger.Error("Logged out successfully but RefreshToken could not be revoked");

            var jwtToken = GetJwtTokenFromRequest();  // Method to extract the JWT from the request headers
            var blacklistResponse = await this.Services_Repo.Userservice.BlacklistJwtToken(jwtToken);
            if (!blacklistResponse)
            {
                _logger.Error("Logged out, but the JWT token could not be invalidated");
            }

            if (result.Succeeded)
            {
                return Ok(new ApiResponse<IdentityResult>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Password changed successfully"
                });
            }
            else
            {
                return BadRequest(new ApiResponse<IdentityResult>
                {
                    Data = result,
                    ResponseCode = "400",
                    ResponseMessage = "Password change failed"
                });
            }
        }

        private static string? ValidatePasswordAgainstPolicy(ChangePasswordDto model, string userName, PasswordPolicy passwordPolicies)
        {
            StringBuilder? errorMessage = null;
            if (passwordPolicies.MinimumPasswordLength != 0 && model.NewPassword.Length < passwordPolicies.MinimumPasswordLength)
                errorMessage.Append($"1. Password must be at least {passwordPolicies.MinimumPasswordLength} characters long. \n");

            if (passwordPolicies.ProhibitUserNameAsPassword && model.NewPassword.ToLower() == userName.ToLower())
                errorMessage.Append("2. Password cannot be the same as the username. \n");

            if (passwordPolicies.RequireAtLeastOneLowercase && !model.NewPassword.Any(char.IsLower))
                errorMessage.Append("3. Password must contain at least one lowercase letter. \n");

            if (passwordPolicies.RequireAtLeastOneUppercase && !model.NewPassword.Any(char.IsUpper))
                errorMessage.Append("4. Password must contain at least one uppercase letter. \n");

            if (passwordPolicies.RequireAtLeastOneNumber && !model.NewPassword.Any(char.IsDigit))
                errorMessage.Append("5. Password must contain at least one digit. \n");

            if (passwordPolicies.RequireAtLeastOneSpecialCharacter && !model.NewPassword.Any(ch => !char.IsLetterOrDigit(ch)))
                errorMessage.Append("6. Password must contain at least one special character. \n");

            return errorMessage?.ToString();
        }
        #endregion

        #region Email Verification Request
        /// <summary>
        /// Request for email verification
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("EmailVerificationRequest")]

        public async Task<ApiResponse<IdentityResult>> EmailVerificationRequest(string email)
        {
            var user = this.UserManager.FindByEmailAsync(email).Result;
            if (user == null)
            {
                return new ApiResponse<IdentityResult> { Data = null, ResponseCode = "00", ResponseMessage = "Email not found!" };
            }

            IdentityResult result = null;
            Random rand = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            string EmailVerificationToken = new string(Enumerable.Repeat(chars, 8)
                .Select(s => s[rand.Next(s.Length)]).ToArray());
            DateTime tokenExpiry = DateTime.UtcNow.AddMinutes(7);
            user.EmailVerificationToken = EmailVerificationToken;
            user.EmailVerificationTokenExpiry = tokenExpiry;
            //Services_Repo.Userservice.up(user);

            // await this.Services_Repo.CommitAsync();
            string msg = $"You have requested Email verification on the CV platform, please contact admin if this wasn't you  <br/>";
            msg += $"Verification Code: {EmailVerificationToken} <br/>";
            msg += $"EXPIRATION TIME {tokenExpiry}";
            await Utils.Utility.SendGridSendMail(msg, email, "Email Verification");

            return new ApiResponse<IdentityResult> { Data = result, ResponseCode = "00", ResponseMessage = "Email Verification Code sent" };
        }
        #endregion

        #region Verify Email Verification Request
        /// <summary>
        /// Verify Email Verification Request
        /// </summary>
        /// <param name="email"></param>
        /// <param name="verificationCode"></param>
        /// <returns></returns>
        [HttpPost("VerifyEmailVerificationRequest")]
        public async Task<ApiResponse<string>> VerifyEmail(string email, string verificationCode)
        {
            User User = await this.UserManager.FindByEmailAsync(email);
            if (User == null) return new ApiResponse<string> { ResponseCode = "404", ResponseMessage = $"User {email} not found" };
            if (verificationCode.Trim() != User.EmailVerificationToken?.Trim())
            {
                return new ApiResponse<string> { ResponseCode = "400", ResponseMessage = "Invalid verification code" };
            }
            if (DateTime.UtcNow > User.EmailVerificationTokenExpiry)
            {
                return new ApiResponse<string> { ResponseCode = "400", ResponseMessage = "Invalid verification code" };
            }
            User.IsEmailVerified = true;
            await this.UserManager.UpdateAsync(User);
            return new ApiResponse<string> { ResponseCode = "00", ResponseMessage = "Successful" };
        }
        #endregion

        #region Send Email - SMTP
        /// <summary>
        /// Send Email
        /// </summary>
        /// <param name="body"></param>
        /// <param name="destination"></param>
        /// <param name="subject"></param>
        [HttpPost]
        [Route("SendEmail")]
        public async void SendEmail(string body, string destination, string subject)
        {
            try
            {
                var email = new MimeMessage();
                email.Sender = MailboxAddress.Parse("<EMAIL>");
                email.To.Add(MailboxAddress.Parse(destination));
                email.Subject = subject;
                var builder = new BodyBuilder();

                builder.HtmlBody = body;
                email.Body = builder.ToMessageBody();
                using var smtp = new SmtpClient();
                smtp.Connect("smtp.gmail.com", 465, SecureSocketOptions.SslOnConnect);
                smtp.Authenticate("<EMAIL>", "fmsonaqaetqtpfzt");
                await smtp.SendAsync(email);
                smtp.Disconnect(true);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        #endregion

        #region Send Email
        /// <summary>
        /// Send Email
        /// </summary>
        /// <param name="body"></param>
        /// <param name="destination"></param>
        /// <param name="subject"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SendGridEmail")]
        public async Task<IActionResult> SendGridSendMail(string body, string destination, string subject)
        {
            try
            {
                var noReplyEmail = Utils.Utility.ApplicationEmailURL.ToLower();
                var plainTextContent = body;
                var htmlContent = $"<strong>{plainTextContent}</strong>";
                // var msg = MailHelper.CreateSingleEmail(from, to, subject, plainTextContent, htmlContent);
                await Utils.Utility.SendGridSendMail(plainTextContent, destination, subject);
                return Ok("Email sent");
            }
            catch (Exception ex)
            {
                return BadRequest(Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE);
            }
        }
        #endregion

        #region Phone Number Verification - Send Token
        /// <summary>
        /// Phone Number Verification
        /// </summary>
        /// <param name="email"></param>
        /// <param name="phonumber"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("PhoneNumberVerificationRequest")]
        public async Task<ApiResponse<IdentityResult>> PhoneNumberVerificationRequest(string email, string phonumber)
        {
            var user = this.UserManager.FindByEmailAsync(email).Result;

            if (user == null)
            {
                return new ApiResponse<IdentityResult> { Data = null, ResponseCode = "200", ResponseMessage = "Email not found!" };
            }

            IdentityResult result = null;
            Random rand = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            string EmailVerificationToken = new string(Enumerable.Repeat(chars, 8)
                .Select(s => s[rand.Next(s.Length)]).ToArray());
            DateTime tokenExpiry = DateTime.UtcNow.AddMinutes(7);
            user.PhoneNumberVerificationToken = EmailVerificationToken;
            user.PhoneNumberVerificationTokenExpiry = tokenExpiry;
            //this.Services_Repo.Userservice.UpdateUserByToken(user);

            try
            {
                string msg = $"Your Validation code is " + EmailVerificationToken;
                SendSMS(phonumber, msg);
            }
            catch (Exception ex)
            {
                Log.Error("Exception caught in sending mail for Email verification", ex.Message);
                return new ApiResponse<IdentityResult> { Data = null, ResponseCode = "500", ResponseMessage = ex.Message };
            }


            return new ApiResponse<IdentityResult> { Data = result, ResponseCode = "200", ResponseMessage = "Verification Code sent" };
        }
        #endregion

        #region Phone Number Token Verification
        /// <summary>
        /// Phone Number Token Verification
        /// </summary>
        /// <param name="email"></param>
        /// <param name="verificationCode"></param>
        /// <returns></returns>
        [HttpPost("PhoneNumberTokenVerification")]
        public async Task<ApiResponse<string>> PhoneNumberTokenVerification(string email, string verificationCode)
        {
            User User = await this.UserManager.FindByEmailAsync(email);
            if (User == null) return new ApiResponse<string> { ResponseCode = "404", ResponseMessage = $"User {email} not found" };
            if (verificationCode.Trim() != User.PhoneNumberVerificationToken?.Trim())
            {
                return new ApiResponse<string> { ResponseCode = "400", ResponseMessage = "Invalid verification code" };
            }
            if (DateTime.UtcNow > User.EmailVerificationTokenExpiry)
            {
                return new ApiResponse<string> { ResponseCode = "400", ResponseMessage = "Invalid verification code" };
            }
            User.IsPhoneNumberVerified = true;
            await this.UserManager.UpdateAsync(User);

            return new ApiResponse<string> { ResponseCode = "200", ResponseMessage = "Successful" };
        }
        #endregion

        #region Send SMS
        /// <summary>
        /// Send SMS
        /// </summary>
        /// <param name="phonumber"></param>
        /// <param name="body"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SendGridSMS")]
        public IActionResult SendSMS(string phonumber, string body)
        {
            try
            {
                // Find your Account SID and Auth Token at twilio.com/console
                // and set the environment variables. See http://twil.io/secure
                string accountSid = "**********************************";
                string authToken = "134e2aa7e61fdde8d4644a2b17a17213";

                TwilioClient.Init(accountSid, authToken);

                var message = MessageResource.Create(
                    body: body,
                    from: new Twilio.Types.PhoneNumber("+***********"),
                    to: new Twilio.Types.PhoneNumber(phonumber)
                );

                return Ok("SMS sent");
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
        #endregion

        #region Generate New Token From Refresh Token
        [AllowAnonymous]
        [HttpPost("refresh-token")]
        public async Task<IActionResult> RefreshToken(RefreshTokenRequest Model)
        {
            var refreshToken = Model.RefreshToken ?? Request.Cookies["refreshToken"];
            UserRefreshToken response = await this.Services_Repo.Userservice.AuthenticateRefreshToken(refreshToken, ipAddress());

            if (response == null)
            {
                return Unauthorized(new { message = "Invalid token" });
            }

            User user = await this.Services_Repo.Userservice.GetUserByRefreshToken(refreshToken);
            var roles = await UserManager.GetRolesAsync(user);
            // replace old refresh token with a new one and save
            var newRefreshToken = await this.GenerateRefreshToken(this.ipAddress());

            // find tenant and company user belongs to and then get the apps the user has access to
            var userCompany = Services_Repo.UserCompaniesServices.GetUserCompany(user.Id, Model.Subdomain);

            response.Revoked = DateTime.UtcNow;
            response.Token = newRefreshToken.Token;
            response.RevokedByIp = ipAddress();
            response.ReplacedByToken = newRefreshToken.Token;
            await Services_Repo.Userservice.AddOrUpdateRefreshToken(response);

            // generate new jwt
            var jwtToken = this.GenerateJwtTokenAsync(user.Email, user, roles.ToString(), userCompany?.tenant?.Id.ToString(), userCompany?.tenant?.Subdomain);

            this.setTokenCookie(newRefreshToken.Token);

            this.setTokenCookie(newRefreshToken.Token);

            string AccessToken = jwtToken.Result.ToString();
            string RefreshToken = newRefreshToken.Token;

            await CheckAndSendNewDeviceLoginNotification(user, ipAddress());

            return Ok(new { AccessToken, RefreshToken });
        }
        #endregion

        #region New Device Login Notification
        /// <summary>
        /// Checks if the user is logging in from a new device/IP and sends a notification email if needed
        /// </summary>
        /// <param name="user">The user who is logging in</param>
        /// <param name="currentIpAddress">The current IP address of the user</param>
        /// <returns>Task</returns>
        private async Task CheckAndSendNewDeviceLoginNotification(User user, string currentIpAddress)
        {
            try
            {
                var previousIpAddress = user.IpAddress;
                if (!string.IsNullOrEmpty(previousIpAddress) && previousIpAddress != currentIpAddress)
                {
                    // Send new device login email notification
                    string templatePath = Path.Combine(_webHostEnvironment.WebRootPath, "EmailTemplates", "auth", "new_device_login.html");
                    string emailTemplate = System.IO.File.ReadAllText(templatePath);

                    // Replace placeholders in the template
                    string userName = $"{user.FirstName} {user.LastName}";
                    emailTemplate = emailTemplate.Replace("[User Name]", userName);
                    emailTemplate = emailTemplate.Replace("[Date_And_Time]", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC"));
                    
                    // Get browser/device info from User-Agent
                    string userAgent = _httpContextAccessor.HttpContext.Request.Headers["User-Agent"].ToString();
                    emailTemplate = emailTemplate.Replace("[Device_Or_Browser]", userAgent);
                    
                    // Set IP address
                    emailTemplate = emailTemplate.Replace("[IP]", currentIpAddress);
                    
                    // For location, we would need a geolocation service
                    // For now, just use a placeholder
                    emailTemplate = emailTemplate.Replace("[State_Country]", "Unknown Location");

                    // Send the email notification using the email service
                    BackgroundJob.Enqueue(() => _emailService.SendEmail(emailTemplate, user.Email, "New Login Detected - Security Alert"));
                }
                
                // Always update the IP address
                user.IpAddress = currentIpAddress;
                await this.UserManager.UpdateAsync(user);
            }
            catch (Exception ex)
            {
                // Log the error but don't interrupt the login flow
                _logger.Error(ex, "Failed to send new device login email notification");
            }
        }
        #endregion

        #region Revoke Token
        [HttpPost("revoke-token")]
        public async Task<IActionResult> RevokeToken()
        {
            var response = await this.Services_Repo.Userservice.RevokeToken(ipAddress());
            if (!response)
                return NotFound(new { message = "Token could not be revoked" });

            return Ok(new { message = "Token revoked" });
        }
        #endregion

        #region Helper Methods
        private void setTokenCookie(string token)
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Expires = DateTime.UtcNow.AddDays(7)
            };
            Response.Cookies.Append("refreshToken", token, cookieOptions);
        }

        private string ipAddress()
        {
            if (Request.Headers.ContainsKey("X-Forwarded-For"))
                return Request.Headers["X-Forwarded-For"];
            else
                return HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString();
        }

        // create token
        private async Task<object> GenerateJwtTokenAsync(string email, User user, string? role = null, string tenantId = null, string subdomain = null)
        {
            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, email),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.NameIdentifier, user.Id),
                new Claim(ClaimTypes.Locality, user.Region),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim(ClaimTypes.Name, user.FirstName + " " + user.LastName),
            };
            claims.Add(new Claim("userType", user.UserType.GetDisplayName()));

            if (tenantId is not null)
                claims.Add(new Claim("tenantId", tenantId));

            if (subdomain is not null)
                claims.Add(new Claim("subdomain", subdomain));

            var roles = await UserManager.GetRolesAsync(user);

            claims.AddRange(roles.Select(role => new Claim(ClaimsIdentity.DefaultRoleClaimType, role)));

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(JwtokenOptions.Key));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            var expires = DateTime.UtcNow.AddDays(JwtokenOptions.JwtExpireDays);

            var token = new JwtSecurityToken(
                JwtokenOptions.Issuer,
               JwtokenOptions.Audience,
                claims,
                expires: expires,
                signingCredentials: creds
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        private async Task<object> GenerateTempJwtTokenAsync(string userId)
        {
            var claims = new List<Claim>
            {
                new Claim("userId", userId),
            };

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(JwtokenOptions.Key));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            var expires = DateTime.UtcNow.AddMinutes(5);

            var token = new JwtSecurityToken(
                JwtokenOptions.Issuer,
               JwtokenOptions.Audience,
                claims,
                expires: expires,
                signingCredentials: creds
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        private string? ValidateTempJwtTokenAndExtractUserId(string token)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(JwtokenOptions.Key);

            try
            {
                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = JwtokenOptions.Issuer,
                    ValidateAudience = true,
                    ValidAudience = JwtokenOptions.Audience,
                    ValidateLifetime = true, // <-- THIS enables expiration validation
                    ClockSkew = TimeSpan.Zero // No leeway; expire exactly at expiration time
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);

                if (validatedToken is JwtSecurityToken jwtToken)
                {
                    var exp = jwtToken.ValidTo;

                    if (exp < DateTime.UtcNow)
                    {
                        // Token has expired
                        return null;
                    }
                }

                var userId = principal.FindFirst("userId")?.Value;
                return userId;
            }
            catch (SecurityTokenExpiredException)
            {
                // Specific case: token expired
                return null;
            }
            catch
            {
                // Other validation errors
                return null;
            }
        }

        private async Task<UserRefreshToken> GenerateRefreshToken(string ipAddress)
        {
            using (var rngCryptoServiceProvider = new RNGCryptoServiceProvider())
            {
                var randomBytes = new byte[64];
                rngCryptoServiceProvider.GetBytes(randomBytes);
                return new UserRefreshToken
                {
                    Token = Convert.ToBase64String(randomBytes),
                    Expires = DateTime.UtcNow.AddDays(30),
                    Created = DateTime.UtcNow,
                    CreatedByIp = ipAddress
                };
            }
        }

        private async Task<UserInfo> GetUserFromAccessTokenAsync(string token)
        {
            var apiClient = new HttpClient
            {
                BaseAddress = new Uri("https://api.linkedin.com")
            };
            apiClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
            var url = "https://api.linkedin.com/v2/me";
            var response = await apiClient.GetAsync(url);
            var jsonResponse = await response.Content.ReadAsStringAsync();
            var emailUrl = "https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))";
            var emailResponse = await apiClient.GetAsync(emailUrl);
            var profilepictureUrl = "https://api.linkedin.com/v2/me?projection=(id,profilePicture(displayImage~:playableStreams))";
            var profileResponse = await apiClient.GetAsync(profilepictureUrl);
            var email = (JObject)JsonConvert.DeserializeObject(emailResponse.Content.ReadAsStringAsync().Result);
            var profilepic = (JObject)JsonConvert.DeserializeObject(profileResponse.Content.ReadAsStringAsync().Result);
            var res = JsonConvert.DeserializeObject<UserInfo>(jsonResponse);
            var t = (JValue)email["elements"][0]["handle~"]["emailAddress"];
            res.Email = t.Value.ToString();
            var p = (JValue)profilepic["profilePicture"]["displayImage~"]["elements"][0]["identifiers"][0]["identifier"];
            res.UserImage = p.Value.ToString();
            return res;
        }
        #endregion

        #region Save Linkedin User
        //[HttpPost]
        //[Route("SaveLinkedinUser")]
        //public async Task<ApiResponse<UserInfo>> SaveLinkedinUser(string code)//, string state, string error, string error_description, string role)
        //{
        //    if (string.IsNullOrEmpty(code))
        //    {
        //        return new ApiResponse<UserInfo> { ResponseCode = "500", ResponseMessage = "Error with Linkedin connection" };
        //    }

        //    var httpClient = new HttpClient
        //    {
        //        BaseAddress = new Uri("https://www.linkedin.com/")
        //    };
        //    var requestUrl = "https://www.linkedin.com/oauth/v2/accessToken?grant_type=authorization_code&code=" + code + "&redirect_uri=" + _appSettings.LinkedinRedirectUrl + "&client_id=" + _appSettings.LinkedinClientID + "&client_secret=" + _appSettings.LinkedinSecretKey;
        //    var response = await httpClient.GetAsync(requestUrl);
        //    var token = JsonConvert.DeserializeObject<TokenResponse>(await response.Content.ReadAsStringAsync());
        //    this.setTokenCookie(token.Access_token);
        //    UserInfo user = await GetUserFromAccessTokenAsync(token.Access_token);
        //    var Result = await this.UserManager.FindByEmailAsync(user.Email);

        //    if (Result == null)
        //    {
        //        User saveUser = new User
        //        {
        //            Email = user.Email,
        //            FirstName = user.LocalizedFirstName,
        //            LastName = user.LocalizedLastName,
        //            UserName = user.Email,
        //            Created_At = DateTime.UtcNow,
        //            NormalizedEmail = user.Email.ToUpper(),
        //            NormalizedUserName = user.Email.ToUpper(),
        //            ProfileImageUrl = user.UserImage,
        //            Status = "Registered from Linkedin"
        //        };
        //        var result = Services_Repo.Userservice.SaveUser(saveUser);
        //    }
        //    return new ApiResponse<UserInfo> { ResponseCode = "00", ResponseMessage = "Successful", Data = user }; ;
        //}
        #endregion

        #region Lock Ip Address
        [HttpPatch("LockUserIpAddress/{userId}")]
        public async Task<ApiResponse<Object>> LockIpAddress(string userId, [FromBody] LockVm lockVm)
        {
            try
            {
                var result = await this.Services_Repo.Userservice.lockUserIp(userId: userId, lockVm.isLock);

                return new ApiResponse<Object>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<Object>
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                };
            }
        }
        #endregion

        #region Google sign in
        /// <summary>
        /// Google sign in
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("GoogleAuth")]
        public async Task<IActionResult> GoogleAuth(GoogleAuthModel model)
        {
            if (string.IsNullOrEmpty(model.GoogleAuthId))
            {
                return BadRequest(new ApiResponse<UserInfo> { ResponseCode = "400", ResponseMessage = "Error, Please provide Google Auth Id." });
            }

            var Result = await this.UserManager.FindByEmailAsync(model.Email);

            // Check if the user's individual account is deleted or inactive
            if (Result.IndividualUserAccountStatus == IndividualUserAccountStatus.InActive)
                return BadRequest("Login failed, your individual account is currently inactive. Re-activate to be able to use your individual account.");
            if (Result.IndividualUserAccountStatus == IndividualUserAccountStatus.Deleted)
                return BadRequest("Login failed. Account no longer exists.");

            IPAddress remoteIpAddress = Request.HttpContext.Connection.RemoteIpAddress;
            User saveUser;
            if (Result == null)
            {
                // no user account found
                return BadRequest(new ApiResponse<dynamic>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Account not found, please sign up first.",
                    Data = new { AccountType = "NotFound" }
                });
            }
            else
            {
                if (string.IsNullOrEmpty(Result.GoogleAuthId))
                {
                    return BadRequest(new ApiResponse<dynamic>
                    {
                        ResponseCode = "409",
                        ResponseMessage = "Traditional account found, please use the username and password login feature.",
                        Data = new { AccountType = "TraditionalAccount" }
                    });
                }
                saveUser = Result;
            }

            var ipAdress = this.Services_Repo.Userservice.GetIp(remoteIpAddress);
            var validateResult = await this.Services_Repo.Userservice.ValidateUserIp(saveUser, ipAdress);
            if (!validateResult)
            {
                return BadRequest(new ApiResponse<UserInfo>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid Ip Address",
                    Data = null
                });
            }

            try
            {
                // validate google token
                var GetUserByGoogleToken = await Utils.Utility.GetUserByGoogleToken(model.GoogleAuthToken);
                if (GetUserByGoogleToken == null)
                {
                    return BadRequest(new ApiResponse<UserInfo>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invalid Google Authentication request, please try again.",
                        Data = null,
                    });
                }

                if (GetUserByGoogleToken.Email != model.Email)
                {
                    return BadRequest(new ApiResponse<UserInfo>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invalid request, Email address provided do not match record.",
                        Data = null,
                    });
                }

                var result = await this.MakeAccessToken(saveUser);
                await Services_Repo.Userservice.LogLastLogin(saveUser.Id);

                var userDetails = _mapper.Map<UserDetails>(saveUser);
                var UserType = UserTypes.IndividualUser;
                return Ok(new
                {
                    user = userDetails,
                    saveUser.Id,
                    saveUser.UserName,
                    saveUser.Email,
                    UserType,
                    result.AccessToken,
                    result.RefreshToken,
                    result.Role,
                    result.UserPermissions,
                    result.newRole,
                    result.userCompany,
                    result.UserKycInfo,
                    result.userPermittedApp,
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<UserInfo> { ResponseCode = "500", ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE, Data = null });
            }
        }
        #endregion

        #region Create Access Token for third party login
        private async Task<dynamic> MakeAccessToken(User Model)
        {
            var rootDomain = _appSettings.RootDomain;
            var userCompany = Services_Repo.UserCompaniesServices.GetUserCompany(Model.Id);
            var Role = await _adminService.GetUserRole(Model.Id, userCompany.tenant.Subdomain);
            var userPermissions = await _adminService.GetUserPermissions(Model.Id, userCompany.tenant.Subdomain);
            var AccessToken = await this.GenerateJwtTokenAsync(Model.Email, Model, Role, userCompany?.tenant?.Id.ToString(), userCompany?.tenant?.Subdomain);
            var GeneratedRefreshToken = await this.GenerateRefreshToken(this.ipAddress());
            var RefreshToken = GeneratedRefreshToken.Token;

            ClientRole userRole = null;
            if (!string.IsNullOrEmpty(Model.ClientRoleId.ToString()))
            {
                userRole = await this.Services_Repo.ClientService.GetRoleById(Model.ClientRoleId.ToString());
            }
            var newRole = await this.Services_Repo.ClientService.GetCLientRole(userRole);

            var userPermittedApp = new List<string>();
            if (userCompany != null)
            {
                // Get the applications the user has access to                  
                if (Model.Id != null)
                {
                    await using var context = new JobProDbContext(_conString, new DbContextSchema(userCompany.tenant.Subdomain));
                    var userAppPermissions = (await Services_Repo.TenantService.GetUserAppPermissions(Model.Id, false, userCompany.TenantId.ToString(), context));
                    if (userAppPermissions.Any())
                    {
                        userPermittedApp = userAppPermissions.Select(x => x.Application).ToList();
                    }
                }

                // validate user has access to this schema
                var subDomain = tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                if (userCompany.tenant.Subdomain != tenantSchema.ExtractSubdomainFromRequest(HttpContext) && rootDomain != subDomain && subDomain != "api")
                {
                    throw new Exception("Invalid email or password. User does not exit on this company");
                }
            }
            else
            {
                var userAppPermissions = await Services_Repo.TenantService.GetUserAppPermissions(Model.Id, false, null, null);
                if (userAppPermissions.Any())
                {
                    userPermittedApp = userAppPermissions.Select(x => x.Application).ToList();
                }
            }

            GeneratedRefreshToken.UserId = Model.Id;
            await Services_Repo.Userservice.AddOrUpdateRefreshToken(GeneratedRefreshToken);
            setTokenCookie(RefreshToken);

            return new
            {
                AccessToken,
                RefreshToken,
                Role,
                newRole,
                userCompany,
                userPermittedApp,
                userPermissions
            };
        }
        #endregion

        #region Microsoft Sign in
        [HttpPost("MicrosoftAuth")]
        public async Task<IActionResult> SignIn([FromBody] MicrosoftAuthModel model)
        {
            try
            {
                if (string.IsNullOrEmpty(model.MicrosoftAccessToken))
                {
                    return BadRequest(new ApiResponse<UserInfo> { ResponseCode = "400", ResponseMessage = "Error, Please provide Microsoft Access Token." });
                }

                // User saveUser;
                var user = await Services_Repo.Userservice.GetUserByMicrosoftGraphAccessToken(model);
                if (user == null)
                {
                    // no user account found
                    return BadRequest(new ApiResponse<dynamic> { ResponseCode = "404", ResponseMessage = "Account not found, please sign up first.", Data = new { AccountType = "NotFound" } });
                }
                else
                {
                    if (string.IsNullOrEmpty(user.MicrosoftAuthId))
                    {
                        return BadRequest(new ApiResponse<dynamic> { ResponseCode = "409", ResponseMessage = "Traditional account found, please use the username and password login feature.", Data = new { AccountType = "TraditionalAccount" } });
                    }

                    IPAddress remoteIpAddress = Request.HttpContext.Connection.RemoteIpAddress;
                    var ipAdress = this.Services_Repo.Userservice.GetIp(remoteIpAddress);
                    var validateResult = await this.Services_Repo.Userservice.ValidateUserIp(user, ipAdress);
                    if (!validateResult)
                    {
                        return BadRequest(new ApiResponse<UserInfo> { ResponseCode = "400", ResponseMessage = "Invalid Ip Address", Data = null });
                    }

                    // Check if the user's individual account is deleted or inactive
                    if (user.IndividualUserAccountStatus == IndividualUserAccountStatus.InActive)
                        return BadRequest("Login failed, your individual account is currently inactive. Re-activate to be able to use your individual account.");
                    if (user.IndividualUserAccountStatus == IndividualUserAccountStatus.Deleted)
                        return BadRequest("Login failed. Account no longer exists.");

                    // Log login activity
                    await Services_Repo.Userservice.LogLastLogin(user.Id);

                    var result = await this.MakeAccessToken(user);
                    var userDetails = _mapper.Map<UserDetails>(user);
                    var UserType = UserTypes.IndividualUser;
                    return Ok(new
                    {
                        user = userDetails,
                        user.Id,
                        user.UserName,
                        user.Email,
                        UserType,
                        result.AccessToken,
                        result.RefreshToken,
                        result.Role,
                        result.UserPermissions,
                        result.newRole,
                        result.userCompany,
                        result.UserKycInfo,
                        result.userPermittedApp,
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
                return BadRequest(new ApiResponse<UserInfo> { ResponseCode = "500", ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE, Data = null });
            }
        }
        #endregion

        #region Private Methods
        private string GetJwtTokenFromRequest()
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (authHeader != null && authHeader.StartsWith("Bearer "))
            {
                return authHeader.Substring("Bearer ".Length).Trim();
            }
            return null;
        }

        private async Task<bool> DeleteUserProfile(string subdomain, User User)
        {
            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            var userPro = await context.UserProfiles.FirstOrDefaultAsync(x => x.UserId == User.Id);
            if (userPro != null)
            {
                context.UserProfiles.Remove(userPro);
                await context.SaveChangesAsync();
            }

            return true;
        }

        private async Task<GenericResponse> VerifySocialAuth(dynamic Model)
        {
            if (!string.IsNullOrEmpty(Model.GoogleAuthToken))
            {
                GoogleJsonWebSignature.Payload GetUserByGoogleToken = await Utility.GetUserByGoogleToken
                    (Model.GoogleAuthToken);
                if (GetUserByGoogleToken == null)
                {
                    return new GenericResponse
                    {
                        ResponseCode = "500",
                        ResponseMessage = "Invalid Google Authentication request, please try again.",
                        Data = null,
                    };
                }
                else if (GetUserByGoogleToken.Email != Model.Email)
                {
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invalid request, Email address provided do not match record.",
                        Data = null,
                    };
                }

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Success",
                    Data = GetUserByGoogleToken,
                };
            }

            if (!string.IsNullOrEmpty(Model.MicrosoftAccessToken))
            {
                var MicrosoftUser = await Utils.Utility.GetMicrosoftUserModelAsync(Model.MicrosoftAccessToken);
                if (MicrosoftUser == null)
                {
                    return new GenericResponse
                    {
                        ResponseCode = "500",
                        ResponseMessage = "Invalid Microsoft Authentication request, please try again.",
                        Data = null,
                    };
                }
                else if (MicrosoftUser.Sub != Model.MicrosoftAuthId)
                {
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invalid request, Account mismatch.",
                        Data = null,
                    };
                }

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Success",
                    Data = MicrosoftUser,
                };
            }

            return null;
        }
        #endregion

        #region Get Team Members
        /// <summary>
        /// Gets all team members. Gets a list of all team members with a search parameter
        /// </summary>
        /// <param name="nameParam"></param>
        /// <returns></returns>
        [HttpGet("GetTeamMembers")]
        public async Task<IActionResult> GetTeamMembers(string? nameParam)
        {
            var subdomain = tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            var teamMembers = await this.Services_Repo.Userservice.GetTeamMembers(nameParam, subdomain);
            if (teamMembers == null)
            {
                return BadRequest(new ApiResponse<UserMDVm>
                {
                    ResponseCode = "200",
                    ResponseMessage = "No User was found.",
                    Data = null
                });
            }

            return Ok(new ApiResponse<List<UserMDVm>>
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = teamMembers
            });
        }
        #endregion

        #region Get Team Members Details
        /// <summary>
        /// Get team members details
        /// </summary>
        /// <param name="userIds"></param>
        /// <returns></returns>
        [HttpPost("GetTeamMembersDetails")]
        public async Task<IActionResult> GetTeamMembersDetails([FromBody] List<string> userIds)
        {
            var subdomain = tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            var response = await this.Services_Repo.Userservice.GetTeamMemberDetails(userIds, subdomain);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region Get All Micro services
        [HttpGet("GetAllMicroServices")]
        public async Task<IActionResult> GetAllMicroServices()
        {
            var microServices = await this.Services_Repo.Userservice.GetAllServices();
            if (microServices == null)
            {
                return BadRequest(new ApiResponse<MicroService>
                {
                    ResponseCode = "200",
                    ResponseMessage = "No Micro Service was found.",
                    Data = null
                });
            }

            return Ok(microServices);
        }
        #endregion

        #region Get User Subdomain using Email
        /// <summary>
        /// Get user sundomain using email address
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        [HttpGet("GetUserSubdomain")]
        public async Task<IActionResult> GetUserSubdomain(string email)
        {
            var response = await this.Services_Repo.Userservice.GetUserSubdomain(email);

            if (response.Data == null)
            {
                if (email.IsPersonalEmail())
                {
                    return Ok(new ApiResponse<string>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Success",
                        Data = "api"
                    });
                }
            }

            return Ok(response);
        }
        #endregion

        #region Test rabbitmq publish message method
        [HttpPost("PublishMessage")]
        public async Task<IActionResult> PublishMessage([FromBody] PublishModel model)
        {
            var publishModel = new PublishModel
                 (
                     model.ExchangeName,
                     model.RoutingKey,
                     ExchangeType.Direct,
                     new
                     {
                         Name = "Amaka",
                         Age = 45,
                     }
                 );

            var eventRes = await _publisherService.GenericPublish(publishModel);
            if (!eventRes)
                _logger.Error("Employee onboarded but event could not be published");

            return Ok(new ApiResponse<string>
            {
                ResponseCode = "200",
                ResponseMessage = "Message published successfully",
                Data = null
            });
        }
        #endregion

        #region Publish Already created users events
        [HttpPost("PublishUserEvents/{personalEmail}")]
        public async Task<IActionResult> PublishUserEvents([FromRoute] string personalEmail)
        {
            var users = await this.Services_Repo.Userservice.GetAllUsers(personalEmail);
            if (users.Count == 0)
            {
                return BadRequest(new ApiResponse<string>
                {
                    ResponseCode = "400",
                    ResponseMessage = "No user found",
                    Data = null
                });
            }

            foreach (var user in users)
            {
                var eventModel = new PublishModel
                    (
                        RabbitMQConstants.UserCreatedEvent,
                        "",
                        ExchangeType.Fanout,
                        user
                    );

                var eventRes = await _publisherService.GenericPublish(eventModel);
                if (!eventRes)
                    _logger.Error("Employee onboarded but event could not be published");
            }

            return Ok(new ApiResponse<string>
            {
                ResponseCode = "200",
                ResponseMessage = "Message published successfully",
                Data = null
            });
        }
        #endregion

        #region Publish company/tennts created events
        [HttpPost("PublishTenatCreatedEvents/{subdomain}")]
        public async Task<IActionResult> PublishTenantCreatedEvents([FromRoute] string subdomain)
        {
            var tenatDetails = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Subdomain.ToLower() == subdomain.ToLower());

            var eventPayload = _mapper.Map<TenantDetailsVM>(tenatDetails);
            var eventModel = new PublishModel
                    (
                        RabbitMQConstants.TenantCreatedEvent,
                        "",
                        ExchangeType.Fanout,
                        eventPayload
                    );

            var eventRes = await _publisherService.GenericPublish(eventModel);
            if (!eventRes)
                _logger.Error("Tenenat created event could not be published");

            return Ok(new ApiResponse<string>
            {
                ResponseCode = "200",
                ResponseMessage = "Message published successfully",
                Data = null
            });
        }
        #endregion
    }
}