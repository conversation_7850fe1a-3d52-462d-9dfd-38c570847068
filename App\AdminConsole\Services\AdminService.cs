﻿#region Using Statements
using AutoMapper;
using Jobid.App.ActivityLog.Model;
using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Dto.External;
using Jobid.App.AdminConsole.DTO;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.AdminConsole.Models;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.HelperFiles;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Notification.Hubs;
using Jobid.App.RabbitMQ;
using Jobid.App.Tenant.Contract;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Jobid.App.Tenant.ViewModel;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RabbitMQ.Client;
using RestSharp;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WatchDog;
using static Jobid.App.JobProject.Enums.Enums;
using static Jobid.App.RabbitMQ.Records;
using static Jobid.App.Subscription.Enums.Enums;
using static System.Globalization.CultureInfo;
#endregion

namespace Jobid.App.AdminConsole.Services
{
    public class AdminService : IAdminService
    {
        private JobProDbContext Db;
        private JobProDbContext subdomainSchemaContext;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly IWebHostEnvironment _environment;
        private readonly IEmailService _emailService;
        private readonly IServiceScopeFactory _serviceProvider;
        private readonly string _userId;
        private readonly IMapper _mapper;
        private readonly IPublisherService _publisherService;
        private readonly IRedisCacheService _redisCacheService;
        private readonly string _redisKey;
        private readonly ITenantService _tenantService;
        private readonly IApiCallService _apiCallService;
        private readonly CustomAppSettings _appSettings;
        private readonly IConfiguration _configuration;
        private readonly IHubContext<NotificationHub> _hubContext;
        private readonly ILogger _logger = Log.ForContext<AdminService>();

        public AdminService(JobProDbContext publicSchemaContext, JobProDbContext subdomainSchemaContext, IAWSS3Sevices aWSS3Sevices,
            IWebHostEnvironment environment, IEmailService emailService, IServiceScopeFactory serviceProvider, IMapper mapper, IPublisherService publisherService, IRedisCacheService redisCacheService, ITenantService tenantService, IApiCallService apiCallService, IConfiguration configuration, IHubContext<NotificationHub> hubContext)
        {
            Db = publicSchemaContext;
            this.subdomainSchemaContext = subdomainSchemaContext;

            _aWSS3Sevices = aWSS3Sevices;
            _environment = environment;
            _emailService = emailService;
            _serviceProvider = serviceProvider;
            _userId = GlobalVariables.LoggedInUserId;
            _mapper = mapper;
            _publisherService = publisherService;
            _redisCacheService = redisCacheService;
            _redisKey = $"{GlobalVariables.Subdomain}-client-admin";
            _tenantService = tenantService;
            _apiCallService = apiCallService;
            _appSettings = GlobalVariables.CustomAppSettings;
            _configuration = configuration;
            _hubContext = hubContext;
        }

        #region Get Activity Logs With Filters
        public async Task<GenericResponse> GetActivitiesWithFiltersForClientAdmin(AcitivityLogFiltersDto filters)
        {
            var activities = new List<Activity>();
            DateTime? sDate = null;
            DateTime? eDate = null;
            DateTime currentDate = DateTime.UtcNow;
            if (filters.Duration == null)
                filters.Duration = ActivityLogDuration.Today;

            // Get all the activities that happened within the duration supplied
            switch (filters.Duration)
            {
                case ActivityLogDuration.Today:
                    var startOfDay = new DateTime(currentDate.Year, currentDate.Month, currentDate.Day, 0, 0, 0);
                    sDate = startOfDay;
                    eDate = currentDate;

                    break;
                case ActivityLogDuration.Yesterday:
                    var yesterday = DateTime.UtcNow.AddDays(-1);
                    var startOfYesterday = new DateTime(yesterday.Year, yesterday.Month, yesterday.Day, 0, 0, 0);
                    var endOfYesterday = new DateTime(yesterday.Year, yesterday.Month, yesterday.Day, 23, 59, 59);
                    sDate = startOfYesterday;
                    eDate = endOfYesterday;

                    break;
                case ActivityLogDuration.ThisWeek:
                    var cDate = DateTime.UtcNow;
                    var startOfWeek = cDate.AddDays(-(int)cDate.DayOfWeek + 1);
                    var endOfWeek = startOfWeek.AddDays(6).AddSeconds(-1);
                    sDate = startOfWeek;
                    eDate = endOfWeek;

                    break;
                case ActivityLogDuration.LastWeek:
                    var startOfCurrentWeek = currentDate.AddDays(-(int)currentDate.DayOfWeek + (int)DayOfWeek.Monday);
                    var endOfCurrentWeek = startOfCurrentWeek.AddDays(7).AddSeconds(-1);
                    var startOfLastWeek = startOfCurrentWeek.AddDays(-7);
                    var endOfLastWeek = startOfLastWeek.AddDays(7).AddSeconds(-1);
                    sDate = startOfLastWeek;
                    eDate = endOfLastWeek;

                    break;
                case ActivityLogDuration.ThisMonth:
                    var startOfMonth = new DateTime(currentDate.Year, currentDate.Month, 1);
                    var endOfMonth = startOfMonth.AddMonths(1).AddSeconds(-1);
                    sDate = startOfMonth;
                    eDate = endOfMonth;

                    break;
                case ActivityLogDuration.LastMonth:
                    // Calculate the start and end of the last month
                    var startOfCurrentMonth = new DateTime(currentDate.Year, currentDate.Month, 1);
                    var endOfCurrentMonth = startOfCurrentMonth.AddMonths(1).AddSeconds(-1);

                    var startOfLastMonth = startOfCurrentMonth.AddMonths(-1);
                    var endOfLastMonth = startOfCurrentMonth.AddSeconds(-1);
                    sDate = startOfLastMonth;
                    eDate = endOfLastMonth;

                    break;
                case ActivityLogDuration.ThisYear:
                    var startOfCurrentYear = new DateTime(currentDate.Year, 1, 1);
                    var endOfCurrentYear = startOfCurrentYear.AddYears(1).AddSeconds(-1);
                    sDate = startOfCurrentYear;
                    eDate = endOfCurrentYear;

                    break;
                case ActivityLogDuration.LastYear:
                    var startOfCurrentYr = new DateTime(currentDate.Year, 1, 1);
                    var endOfCurrentYr = startOfCurrentYr.AddYears(1).AddSeconds(-1);
                    var startOfLastYear = startOfCurrentYr.AddYears(-1);
                    var endOfLastYear = endOfCurrentYr.AddYears(-1);
                    sDate = startOfLastYear;
                    eDate = endOfLastYear;

                    break;
                case ActivityLogDuration.All:
                    sDate = new DateTime(2021, 1, 1);
                    eDate = DateTime.UtcNow;

                    break;
                default:
                    break;
            }

            if (filters.EventCategory.Contains(EventCategory.All.ToString()))
                filters.EventCategory = Enum.GetNames(typeof(EventCategory)).ToList();
            var eventCats = filters.EventCategory.Select(x => (EventCategory)Enum.Parse(typeof(EventCategory), x)).ToList();

            activities = await subdomainSchemaContext.Activities
                        .Include(x => x.LogAttachments)
                        .Where(x => x.CreatedAt >= sDate && x.CreatedAt <= eDate && x.Application == filters.Package && eventCats.Contains(x.EventCategory)).ToListAsync();

            if (filters.TeamIds.Any())
            {
                foreach (var teamId in filters.TeamIds)
                {
                    var teamMemberIds = await subdomainSchemaContext.TeamMembers.Where(x => x.Id.ToString() == teamId)
                            .Select(teamMember => teamMember.UserId).ToListAsync();
                    filters.MemberIds.AddRange(teamMemberIds);
                    filters.MemberIds = filters.MemberIds.Distinct().ToList();
                }
            }

            if (filters.MemberIds.Any())
                activities = activities.Where(x => filters.MemberIds.Contains(x.UserId)).ToList();

            var filteredActivities = activities.ToPageList(filters.PageNumber, filters.PageSize);
            var response = new Dictionary<string, List<Activity>>();

            // Group activities by CreatedAt and then loop through each group
            filteredActivities.Items.GroupBy(x => x.CreatedAt.Date).ToList().ForEach(group =>
            {
                var key = group.Key.ToString();
                if (response.ContainsKey(key))
                {
                    response[key].AddRange(group.ToList().OrderByDescending(o => o.CreatedAt));
                }
                else
                {
                    response.Add(key, group.OrderByDescending(x => x.CreatedAt).ToList());
                }
            });

            return new GenericResponse
            {
                ResponseCode = "200",
                Data = new
                {
                    filteredActivities.PageNumber,
                    filteredActivities.PageSize,
                    filteredActivities.TotalSize,
                    Activities = response,
                },
                ResponseMessage = "Activity records found",
            };
        }
        #endregion

        #region Get User Role
        public async Task<string> GetUserRole(string userId, string subdomain = null)
        {
            JobProDbContext context = null;
            if (subdomain is not null)
            {
                context = new JobProDbContext(new DbContextSchema(subdomain));
            }
            else
            {
                context = subdomainSchemaContext;
            }

            var role = await context.UserAndRoleIds
                .Include(i => i.EmployeeRoles)
                .Where(x => x.UserProId == userId)
                .Select(s => s.EmployeeRoles.RoleName)
                .FirstOrDefaultAsync();

            return role;
        }
        #endregion

        #region Get User Permissions
        public async Task<List<string>> GetUserPermissions(string userId, string subdomain = null)
        {
            JobProDbContext context = null;
            if (subdomain is not null)
                context = new JobProDbContext(new DbContextSchema(subdomain));
            else
                context = subdomainSchemaContext;

            var roleIds = await context.UserAndRoleIds
                .Where(x => x.UserProId == userId)
                .Select(s => s.RoleId)
                .ToListAsync();

            var permissions = await context.EmployeeRolesPermissions
                .Include(x => x.EmployeePermission)
                .Where(x => roleIds.Contains(x.RoleId))
                .Select(s => s.EmployeePermission.PermissionName)
                .ToListAsync();

            return permissions;
        }
        #endregion

        #region Get Active Employees Count
        public async Task<long> GetActiveEmployeeCount()
        {
            return await subdomainSchemaContext.UserProfiles
                .Where(x => !x.IsSuspended).CountAsync();
        }
        #endregion

        #region Get Company Logo
        public async Task<string> GetAdminCompanyLogo(string subdomain)
        {
            var tenant = await Db.Tenants.FirstOrDefaultAsync(x => x.Subdomain == subdomain)
                ?? throw new RecordNotFoundException("Subdomain not found");

            var logoUrl = tenant.LogoUrl != null ? await _aWSS3Sevices.GetSignedUrlAsync(tenant.LogoUrl) : null;
            return logoUrl;
        }
        #endregion

        #region Update Company Logo
        public async Task<bool> UpdateAdminCompanyLogo(string subdomain, UpdateCompanyLogoDto model)
        {
            var tenant = await Db.Tenants
                .FirstOrDefaultAsync(x => x.Subdomain.ToLower() == subdomain.ToLower()) ?? throw new RecordNotFoundException("Subdomain not found");

            var fileName = tenant.Subdomain + "-Logo." + model.Logo.FileName;
            var response = await _aWSS3Sevices.UploadFileAsync(model.Logo, fileName);
            if (response == null)
                return false;

            tenant.LogoUrl = fileName;
            tenant.LastUpdate = DateTime.UtcNow;

            Db.Update(tenant);
            var result = await Db.SaveChangesAsync() > 0;

            // Log Activity
            var res = await LogActivity(_userId, "Company logo updated", "Company logo updated", tenant.Id.ToString());
            if (!res)
                WatchLogger.LogError("Failed to log activity", "AdminService", "UpdateAdminCompanyLogo", "Failed to log activity");

            return result;
        }
        #endregion

        #region Update Comany Profile
        public async Task<bool> UpdateAdminCompanyProfile(string subdomain, UpdateAdminProfileDto profileDto)
        {
            var tenant = await Db.Tenants.Include(t => t.Admin).FirstOrDefaultAsync(x => x.Subdomain == subdomain)
                ?? throw new RecordNotFoundException("Subdomain not found");

            if (tenant.Admin == null)
                throw new RecordNotFoundException("Admin not found");

            tenant.Admin.FirstName = profileDto.FirstName;
            tenant.Admin.LastName = profileDto.LastName;
            tenant.Admin.Email = profileDto.Email;
            tenant.WorkSpace = profileDto.WorkSpace;
            tenant.CompanyName = profileDto.WorkSpace;

            var userProfile = await subdomainSchemaContext.UserProfiles
                .FirstOrDefaultAsync(x => x.UserId == tenant.Admin.Id)
                ?? throw new RecordNotFoundException("User profile not found");

            userProfile.FirstName = profileDto.FirstName;
            userProfile.LastName = profileDto.LastName;
            userProfile.Email = profileDto.Email;

            Db.Update(tenant);
            subdomainSchemaContext.Update(userProfile);
            var dbResult = await subdomainSchemaContext.SaveChangesAsync();
            var result = await Db.SaveChangesAsync() > 0;
            if (!result || dbResult < 1)
            {
                return false;
            }

            // Publish an event to update the company profile in all the schemas
            var tenantDtoToPublish = _mapper.Map<TenantDetailsVM>(tenant);
            var eventModel = new PublishModel
            (
                RabbitMQConstants.TenantUpdatedEvent,
                "",
                ExchangeType.Fanout,
                tenantDtoToPublish
            );

            var eventRes = await _publisherService.GenericPublish(eventModel);
            if (!eventRes)
            {
                WatchLogger.LogError("Tenant Created but event could not be published", "Error", "TenantService", "RegisterTenant");
                _logger.Error("Tenant Created but event could not be published");
            }

            // Log Activity
            var res = await LogActivity(_userId, "Company profile updated", "Company profile updated", tenant.Id.ToString());
            if (!res)
                WatchLogger.LogError("Failed to log activity", "AdminService", "UpdateAdminCompanyProfile", "Failed to log activity");

            return result;
        }
        #endregion

        #region Give user full access previledge
        public async Task<GenericResponse> GiveUserFullAccess(GiveUserFullAccessDto model)
        {
            try
            {
                var userAccess = await _tenantService.GetUserAppPermissions(model.UserId, false, model.TenantId);
                AppPermissions specificAccess = null;

                if (model.Agent is null)
                    specificAccess = userAccess.Where(x => x.Application == model.App).FirstOrDefault();
                else
                    specificAccess = userAccess.Where(x => x.Agent == model.Agent).FirstOrDefault();

                if (specificAccess is not null)
                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = "User already has access",
                        Data = specificAccess
                    };

                // Give full access
                var response = await _tenantService.AddUserToAppPermission(model.UserId, model.App, model.LoggedInUserId, model.TenantId, model.Agent);
                if (!response)
                    return new GenericResponse
                    {
                        ResponseCode = "500",
                        ResponseMessage = "Failed, try again later",
                        Data = false
                    };

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Full access granted successsfuly",
                    Data = true
                };
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error("GiveUserFullAccess: Bad request", ex);
                return new GenericResponse
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                };
            }
        }
        #endregion

        #region Downgrade user to basic access
        /// <summary>
        /// Downgrade user to basic access
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> DowngradeUserToBasicAccess(DowngradeUserToBasicAccessDto model)
        {
            var response = await _tenantService.DowngradeUserToBasicAccess(model);
            return response;
        }
        #endregion

        #region Revoke user access
        /// <summary>
        /// This method revokes user access
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> RevokeUserAccess(RevokeUserAccessDto model)
        {
            var response = await _tenantService.RevokeUserAccess(model);
            return response;
        }
        #endregion

        #region Remove user from compoany
        /// <summary>
        /// Delete user from a company
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        public async Task<GenericResponse> RemoveUserFromCompany(string userId, string subdomain)
        {
            var response = await _tenantService.DeleteUserFromCompany(userId, subdomain);
            return response;
        }
        #endregion

        #region Get All Packages
        public async Task<IEnumerable<PackagesDto>> GetAllPackages(string subdomain)
        {
            var tenant = await Db.Tenants.FirstOrDefaultAsync(x => x.Subdomain == subdomain) ?? throw new RecordNotFoundException($"tenant not found");
            var packageSummary = new List<PackagesDto>();
            //var packages = new List<string> { "Job Project", "JobPays", "JobId", "JobCRM", "ProEvent", "JobMatch", "JobEMS" };
            var packages = Enum.GetValues(typeof(Applications)).Cast<Applications>().ToList();
            foreach (var package in packages)
            {
                if (package == Applications.All)
                    continue;

                var status = "InActive";
                if (package == Applications.JobPays || package == Applications.JobID)
                    status = "Active";

                var companySub = await Db.CompanySubscriptions.Where(c => c.TenantId == tenant.Id && c.Application == package).FirstOrDefaultAsync();
                if (companySub == null)
                {
                    var result = new PackagesDto
                    {
                        PackageName = package,
                        PackagePlan = null,
                        Status = status,
                    };
                    packageSummary.Add(result);
                }
                else
                {
                    var subcription = await Db.Subscriptions.FirstOrDefaultAsync(x => x.Id == companySub.SubscriptionId);
                    if (subcription == null)
                    {
                        var result1 = new PackagesDto
                        {
                            PackageName = package,
                            PackagePlan = null,
                            Status = companySub.Status.ToString(),
                        };
                        packageSummary.Add(result1);
                    }
                    else
                    {
                        var packagePlan = await Db.PricingPlans.FirstOrDefaultAsync(p => p.Id == subcription.PricingPlanId);
                        if (packagePlan == null)
                        {
                            var result = new PackagesDto
                            {
                                PackageName = package,
                                PackagePlan = null,
                                Status = companySub.Status.ToString()
                            };
                            packageSummary.Add(result);
                        }
                        else
                        {
                            var result = new PackagesDto
                            {
                                PackageName = package,
                                PackagePlan = packagePlan.Name,
                                Status = companySub.Status.ToString()
                            };
                            packageSummary.Add(result);
                        }
                    }
                }
            }
            return packageSummary;
        }
        #endregion

        #region Get Recent Activities
        public async Task<(IEnumerable<RecentActivitiesDto>, long)> GetAllRecentActivities(int pageNumber, int pageSize)
        {
            var activities = await subdomainSchemaContext.Activities.OrderByDescending(x => x.CreatedAt).ToPageListAsync(pageNumber, pageSize);
            if (activities.TotalSize == 0) { return (null, 0); }
            var result = activities.Items.Select(x => new RecentActivitiesDto
            {
                Activities = x.ActivitySummary,
                Date = x.CreatedAt.ToString("dd MMMM yyyy"),
                Time = x.CreatedAt.ToString("t")
            });
            return (result, activities.TotalSize);
        }
        #endregion

        #region Get Package Details By Month
        public async Task<List<PackageMonthlyUsage>> GetPackageUsageByMonths(MostUsedPackagesFrequency frequency)
        {
            var result = new List<PackageMonthlyUsage>();
            if (frequency == MostUsedPackagesFrequency.FrequentlyUsed)
            {
                var permissions = await subdomainSchemaContext.AppPermissions.Where(x => x.IsEnabled == true).ToListAsync();
                if (permissions.Count == 0) return null;
                var allMonths = CurrentCulture.DateTimeFormat.MonthNames;
                var initialMonthUsage = allMonths.ToDictionary(month => month, _ => 0);
                var groupedPermissions = permissions.GroupBy(p => p.Application).ToList();
                foreach (var group in groupedPermissions)
                {
                    var packageUsage = new PackageMonthlyUsage
                    {
                        PackageName = group.Key,
                        MonthlyUsage = new Dictionary<string, int>(initialMonthUsage)
                    };
                    var groupedByMonth = group.GroupBy(p => p.UpdatedAt.Month).ToList();
                    foreach (var monthGroup in groupedByMonth)
                    {
                        var monthName = CurrentCulture.DateTimeFormat.GetMonthName(monthGroup.Key);
                        packageUsage.MonthlyUsage[monthName] = monthGroup.Count();
                    }
                    result.Add(packageUsage);
                }
                return result;
            }
            else
                return result;
        }
        #endregion

        #region Get used and remaining liecence count
        /// <summary>
        /// Get used and remaining liecence count
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <exception cref="InvalidOperationException"></exception>
        public async Task<GenericResponse> GetUsedAndRemainingLicenceCount(string tenantId, Applications app)
        {
            var result = new Dictionary<string, int>();
            var tenant = await Db.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == tenantId)
                ?? throw new RecordNotFoundException($"tenant not found");

            var usedLicence = await subdomainSchemaContext
                .AppPermissions.Where(x => x.IsEnabled == true && x.SubscriptionStatus == Subscription.Enums.Enums.SubscriptionStatus.Active.ToString())
                .CountAsync();

            var subscription = await Db.Subscriptions
                .Where(x => x.Application == app && x.TenantId.ToString() == tenantId)
                .FirstOrDefaultAsync();

            if (subscription == null)
                throw new RecordNotFoundException("Your not subscribed");

            if (subscription.Status != PaymentStatus.Successful.ToString())
                throw new InvalidOperationException("You don't have an active liecence subscription");

            var remainingLicence = subscription.SubscriptionFor - usedLicence;
            result = new Dictionary<string, int>
            {
                { "UsedLicence", usedLicence },
                { "RemainingLicence", remainingLicence.Value }
            };

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Licence count retrieved successfully",
                Data = result
            };
        }
        #endregion

        #region Get Active Employees Count By Application
        public async Task<Dictionary<string, int>> GetActiveEmployeesByPackages()
        {
            var result = new Dictionary<string, int>();
            var permission = await subdomainSchemaContext.AppPermissions.Where(x => x.IsEnabled == true && !x.IsSuspended).ToListAsync();
            if (permission.Count == 0) return null;

            var groupedPermissions = permission.GroupBy(p => p.Application);
            foreach (var group in groupedPermissions)
            {
                result[group.Key] = group.Count();
            }

            return result;
        }
        #endregion

        #region Get Employee Distribution By Frequency
        public async Task<Dictionary<string, int>> EmployeeDistributionByFrequency(EmployeeDistributionFrequency frequency)
        {
            var permissions = await subdomainSchemaContext.AppPermissions
                    .Where(x => x.IsEnabled == true && !x.IsSuspended)
                    .ToListAsync();
            var result = new Dictionary<string, int>();
            if (frequency == EmployeeDistributionFrequency.ThisMonth)
            {
                var currentDate = DateTime.UtcNow;
                var startOfMonth = new DateTime(currentDate.Year, currentDate.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddSeconds(-1);
                var permissionsForFreq = permissions.Where(x => x.CreatedAt >= startOfMonth && x.CreatedAt <= endOfMonth);
                var groupedPermissions = permissionsForFreq.GroupBy(p => p.Application);

                foreach (var group in groupedPermissions)
                {
                    result[group.Key] = group.Count();
                }
                return result;
            }
            else if (frequency == EmployeeDistributionFrequency.ThisWeek)
            {
                var currentDate = DateTime.UtcNow;
                var startOfWeek = currentDate.AddDays(-(int)currentDate.DayOfWeek + 1);
                var endOfWeek = startOfWeek.AddDays(6).AddSeconds(-1);
                var permissionsForFreq = permissions.Where(x => x.CreatedAt >= startOfWeek && x.CreatedAt <= endOfWeek);
                var groupedPermissions = permissionsForFreq.GroupBy(p => p.Application);
                foreach (var group in groupedPermissions)
                {
                    result[group.Key] = group.Count();
                }
                return result;
            }
            else if (frequency == EmployeeDistributionFrequency.LastWeek)
            {
                var currentDate = DateTime.UtcNow;
                var startOfCurrentWeek = currentDate.AddDays(-(int)currentDate.DayOfWeek + (int)DayOfWeek.Monday);
                var endOfCurrentWeek = startOfCurrentWeek.AddDays(7).AddSeconds(-1);
                var startOfLastWeek = startOfCurrentWeek.AddDays(-7);
                var endOfLastWeek = startOfLastWeek.AddDays(7).AddSeconds(-1);
                var permissionsForFreq = permissions.Where(x => x.CreatedAt >= startOfLastWeek && x.CreatedAt <= endOfLastWeek);

                var groupedPermissions = permissionsForFreq.GroupBy(p => p.Application);
                foreach (var group in groupedPermissions)
                {
                    result[group.Key] = group.Count();
                }

                return result;
            }
            else if (frequency == EmployeeDistributionFrequency.LastMonth)
            {
                var currentDate = DateTime.UtcNow;

                // Calculate the start and end of the last month
                var startOfCurrentMonth = new DateTime(currentDate.Year, currentDate.Month, 1);
                var endOfCurrentMonth = startOfCurrentMonth.AddMonths(1).AddSeconds(-1);

                var startOfLastMonth = startOfCurrentMonth.AddMonths(-1);
                var endOfLastMonth = startOfCurrentMonth.AddSeconds(-1);

                // Filter permissions for the last month
                var permissionsForFreq = permissions.Where(x => x.CreatedAt >= startOfLastMonth && x.CreatedAt <= endOfLastMonth);
                var groupedPermissions = permissionsForFreq.GroupBy(p => p.Application);
                foreach (var group in groupedPermissions)
                {
                    result[group.Key] = group.Count();
                }

                return result;
            }
            else if (frequency == EmployeeDistributionFrequency.LastYear)
            {
                var currentDate = DateTime.UtcNow;

                // Calculate the start and end of the current year
                var startOfCurrentYear = new DateTime(currentDate.Year, 1, 1);
                var endOfCurrentYear = startOfCurrentYear.AddYears(1).AddSeconds(-1);
                var startOfLastYear = startOfCurrentYear.AddYears(-1);
                var endOfLastYear = endOfCurrentYear.AddYears(-1);

                var permissionsForFreq = permissions.Where(x => x.CreatedAt >= startOfLastYear && x.CreatedAt <= endOfLastYear);
                var groupedPermissions = permissionsForFreq.GroupBy(p => p.Application);

                // Populate result dictionary with grouped permission counts
                foreach (var group in groupedPermissions)
                {
                    result[group.Key] = group.Count();
                }

                return result;
            }
            else if (frequency == EmployeeDistributionFrequency.ThisYear)
            {
                var currentDate = DateTime.UtcNow;

                // Calculate the start and end of the current year
                var startOfCurrentYear = new DateTime(currentDate.Year, 1, 1);
                var endOfCurrentYear = startOfCurrentYear.AddYears(1).AddSeconds(-1);

                var permissionsForFreq = permissions.Where(x => x.CreatedAt >= startOfCurrentYear && x.CreatedAt <= endOfCurrentYear);
                var groupedPermissions = permissionsForFreq.GroupBy(p => p.Application);

                // Populate result dictionary with grouped permission counts
                foreach (var group in groupedPermissions)
                {
                    result[group.Key] = group.Count();
                }

                return result;
            }
            else if (frequency == EmployeeDistributionFrequency.ThreeMonthsAgo)
            {
                var currentDate = DateTime.UtcNow;

                // Calculate the start and end of the current month
                var startOfCurrentMonth = new DateTime(currentDate.Year, currentDate.Month, 1);
                var startOfThreeMonthsAgo = startOfCurrentMonth.AddMonths(-3);
                var endOfThreeMonthsAgo = startOfCurrentMonth.AddSeconds(-1);

                // Filter permissions for three months ago
                var permissionsForFreq = permissions.Where(x => x.CreatedAt >= startOfThreeMonthsAgo && x.CreatedAt <= endOfThreeMonthsAgo);
                var groupedPermissions = permissionsForFreq.GroupBy(p => p.Application);
                foreach (var group in groupedPermissions)
                {
                    result[group.Key] = group.Count();
                }

                return result;
            }
            else if (frequency == EmployeeDistributionFrequency.SixMonthsAgo)
            {
                var currentDate = DateTime.UtcNow;

                // Calculate the start of the current month
                var startOfCurrentMonth = new DateTime(currentDate.Year, currentDate.Month, 1);
                var startOfSixMonthsAgo = startOfCurrentMonth.AddMonths(-6);
                var endOfSixMonthsAgo = startOfCurrentMonth.AddSeconds(-1);

                var permissionsForFreq = permissions.Where(x => x.CreatedAt >= startOfSixMonthsAgo && x.CreatedAt <= endOfSixMonthsAgo);
                var groupedPermissions = permissionsForFreq.GroupBy(p => p.Application);
                foreach (var group in groupedPermissions)
                {
                    result[group.Key] = group.Count();
                }

                return result;
            }

            else
                return result;
        }
        #endregion

        #region Get Package Dashboard Data
        public async Task<PackageDashBoard> GetPackagesDashboard(Applications application, string subdomain)
        {
            // Check if redis cache has a value
            var cacheKey = $"admin_{application}_{subdomain}";
            Utility.AddKeyToCacheKeys(_redisKey, cacheKey);
            var todosFromCache = await _redisCacheService.GetDataAsync<PackageDashBoard>(cacheKey);
            if (todosFromCache != null)
                return todosFromCache;

            var permission = await subdomainSchemaContext.AppPermissions
                .Where(x => x.Application == application.ToString() && x.IsEnabled).ToListAsync();
            if (!permission.Any())
            {
                var noResult = new PackageDashBoard
                {
                    Package = application,
                };

                return noResult;
            }

            var tenantId = await Db.Tenants.Where(t => t.Subdomain == subdomain).Select(t => t.Id.ToString()).FirstOrDefaultAsync();
            var activeEmployees = permission.Where(x => x.IsEnabled && !x.IsSuspended).Count();
            var inActiveEmployees = permission.Where(x => !x.IsEnabled || x.IsSuspended).Count();
            var pendingInvitations = await Db.CompanyUserInvites
                .Where(x => x.Application == application && x.Status == "pending-finalization" && x.TenantId == tenantId)
                .ToListAsync();
            var pendingInvitationCount = pendingInvitations.Count();
            var totalPermissions = activeEmployees + inActiveEmployees;
            if (totalPermissions == 0)
            {
                var noResult = new PackageDashBoard
                {
                    Package = application,
                };
                return noResult;
            }
            var activePencentage = activeEmployees / totalPermissions * 100;
            var inActivePencentage = inActiveEmployees / totalPermissions * 100;
            var pendingPencentage = pendingInvitationCount / totalPermissions * 100;

            var currentDate = DateTime.UtcNow;
            var startOfWeek = currentDate.AddDays(-((int)currentDate.DayOfWeek + 1));
            var endOfWeek = startOfWeek.AddDays(6);
            var activeForThisWeek = permission.Where(x => x.UpdatedAt >= startOfWeek && x.UpdatedAt <= endOfWeek && !x.IsSuspended).Count();
            var inActiveForThisWeek = permission.Where(x => x.UpdatedAt >= startOfWeek && x.UpdatedAt <= endOfWeek && !x.IsSuspended).Count();
            var pendingForThisWeek = pendingInvitations.Where(x => x.DateCreated >= startOfWeek && x.DateCreated <= endOfWeek).Count();

            var result = new PackageDashBoard
            {
                Package = application,
                TotalActiveEmployees = activeEmployees,
                PercentageActiveEmployees = activePencentage,
                TotalActiveEmployeesThisWeek = activeForThisWeek,
                TotalInActiveEmployees = inActiveEmployees,
                PercentageInActiveEmployees = inActivePencentage,
                TotalInActiveEmployeesThisWeek = inActiveForThisWeek,
                TotalPendingInvitations = pendingInvitationCount,
                PercentagePendingInvitations = pendingPencentage,
                TotalPendingInvitationsThisWeek = pendingForThisWeek,
            };

            // Add to redis cache
            await AddDataToRedis(cacheKey, result);
            return result;
        }
        #endregion

        #region Get User Distribution By Location
        public async Task<GenericResponse> GetUserDistributionByLocation(EmployeeDistributionFrequency frequency, Applications application = Applications.Joble)
        {
            var result = new UserLocationDistributionDto();
            var countryDistribution = new Dictionary<string, int>();

            // Get date range based on frequency
            var (startDate, endDate) = GetDateRangeFromFrequency(frequency);

            // Get users with permissions for the specified application within the date range
            var permissions = await subdomainSchemaContext.AppPermissions
                .Where(x => x.IsEnabled == true
                       && !x.IsSuspended
                       && x.Application == application.ToString()
                       && x.CreatedAt >= startDate
                       && x.CreatedAt <= endDate)
                .ToListAsync();

            if (permissions.Count == 0)
            {
                return new GenericResponse();
            }

            // Get user IDs from permissions
            var userIds = permissions.Select(p => p.UserId).Distinct().ToList();
            result.TotalUsers = userIds.Count;

            // Get user profiles with country information
            var userProfiles = await subdomainSchemaContext.UserProfiles
                .Where(u => userIds.Contains(u.UserId) && !u.IsDeleted)
                .Select(u => new { u.UserId, u.Country })
                .ToListAsync();

            // Get users with country information from profiles
            var usersWithCountry = userProfiles
                .Where(u => !string.IsNullOrEmpty(u.Country))
                .ToList();

            // Get users without country information
            var usersWithoutCountry = userIds
                .Except(usersWithCountry.Select(u => u.UserId))
                .ToList();

            // Group users by country
            var countryGroups = usersWithCountry
                .GroupBy(u => u.Country)
                .ToDictionary(g => g.Key, g => g.Count());

            // Add country groups to result
            foreach (var group in countryGroups)
            {
                countryDistribution[group.Key] = group.Value;
            }

            if (usersWithoutCountry.Any())
            {
                var usersWithPhones = await subdomainSchemaContext.UserProfiles
                    .Where(u => usersWithoutCountry.Contains(u.UserId) && !string.IsNullOrEmpty(u.PhoneNumber))
                    .Select(u => new { u.UserId, u.PhoneNumber })
                    .ToListAsync();

                // Use helper CountryCodeMap
                var phoneCountryGroups = new Dictionary<string, int>();
                foreach (var user in usersWithPhones)
                {
                    var phone = user.PhoneNumber;
                    string foundCountry = null;
                    if (!string.IsNullOrEmpty(phone))
                    {
                        // Try to match the longest country code first                          
                        var match = CountryCodeMap.CodeToCountry.Keys.OrderByDescending(k => k.Length)
                            .FirstOrDefault(code => phone.StartsWith(code));

                        if (match != null)
                        {
                            foundCountry = CountryCodeMap.CodeToCountry[match];
                        }
                    }
                    if (!string.IsNullOrEmpty(foundCountry))
                    {
                        if (phoneCountryGroups.ContainsKey(foundCountry))
                            phoneCountryGroups[foundCountry]++;
                        else
                            phoneCountryGroups[foundCountry] = 1;
                    }
                }
                // Add or update country distribution from phone number
                foreach (var group in phoneCountryGroups)
                {
                    if (countryDistribution.ContainsKey(group.Key))
                        countryDistribution[group.Key] += group.Value;
                    else
                        countryDistribution[group.Key] = group.Value;
                }
                // Update users with location count
                var usersWithLocationFromPhone = countryDistribution.Values.Sum();
                result.UsersWithLocation += usersWithLocationFromPhone;
                result.UsersWithoutLocation = result.TotalUsers - result.UsersWithLocation;
            }
            else
            {
                result.UsersWithLocation = usersWithCountry.Count;
                result.UsersWithoutLocation = result.TotalUsers - result.UsersWithLocation;
            }

            result.CountryDistribution = countryDistribution;
            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "User distribution by location retrieved successfully",
                Data = result
            };
        }

        private (DateTime startDate, DateTime endDate) GetDateRangeFromFrequency(EmployeeDistributionFrequency frequency)
        {
            var currentDate = DateTime.UtcNow;
            var startDate = currentDate;
            var endDate = currentDate;

            switch (frequency)
            {
                case EmployeeDistributionFrequency.ThisMonth:
                    startDate = new DateTime(currentDate.Year, currentDate.Month, 1);
                    endDate = startDate.AddMonths(1).AddSeconds(-1);
                    break;

                case EmployeeDistributionFrequency.ThisWeek:
                    startDate = currentDate.AddDays(-(int)currentDate.DayOfWeek + 1);
                    endDate = startDate.AddDays(6).AddSeconds(-1);
                    break;

                case EmployeeDistributionFrequency.ThisYear:
                    startDate = new DateTime(currentDate.Year, 1, 1);
                    endDate = startDate.AddYears(1).AddSeconds(-1);
                    break;

                case EmployeeDistributionFrequency.LastMonth:
                    startDate = new DateTime(currentDate.Year, currentDate.Month, 1).AddMonths(-1);
                    endDate = new DateTime(currentDate.Year, currentDate.Month, 1).AddSeconds(-1);
                    break;

                case EmployeeDistributionFrequency.LastWeek:
                    var startOfCurrentWeek = currentDate.AddDays(-(int)currentDate.DayOfWeek + (int)DayOfWeek.Monday);
                    startDate = startOfCurrentWeek.AddDays(-7);
                    endDate = startOfCurrentWeek.AddSeconds(-1);
                    break;

                case EmployeeDistributionFrequency.LastYear:
                    startDate = new DateTime(currentDate.Year - 1, 1, 1);
                    endDate = new DateTime(currentDate.Year, 1, 1).AddSeconds(-1);
                    break;

                case EmployeeDistributionFrequency.ThreeMonthsAgo:
                    startDate = new DateTime(currentDate.Year, currentDate.Month, 1).AddMonths(-3);
                    endDate = startDate.AddMonths(1).AddSeconds(-1);
                    break;

                case EmployeeDistributionFrequency.SixMonthsAgo:
                    startDate = new DateTime(currentDate.Year, currentDate.Month, 1).AddMonths(-6);
                    endDate = startDate.AddMonths(1).AddSeconds(-1);
                    break;
            }

            return (startDate, endDate);
        }
        #endregion

        #region Get Pending Invitations Count
        public async Task<int> GetPendingInvitationsCount(string subdomain = null, Applications? app = null)
        {
            var totalInviteCount = 0;
            if (app is not null)
            {
                totalInviteCount = await Db.CompanyUserInvites
                    .Where(u => u.Status == "pending-finalization" && u.Application == app).CountAsync();
            }
            else
            {
                var tenantId = await Db.Tenants.Where(t => t.Subdomain == subdomain).Select(t => t.Id.ToString()).FirstOrDefaultAsync();
                totalInviteCount = await Db.CompanyUserInvites
                    .Where(u => u.Status == "pending-finalization" && u.TenantId == tenantId).CountAsync();
            }

            return totalInviteCount;
        }
        #endregion

        #region Get Pending Invitations
        public async Task<GenericResponse> GetPendingInvitations(string subdomain, Applications? app = null, int pageNumber = 1, int pageSize = 10)
        {
            Page<CompanyUserInvite> totalInvites = null;
            var tenantId = await Db.Tenants.Where(t => t.Subdomain == subdomain).Select(t => t.Id.ToString()).FirstOrDefaultAsync();
            if (app is not null)
            {
                totalInvites = await Db.CompanyUserInvites
                    .Where(u => u.Status == "pending-finalization" && u.Application == app && u.TenantId == tenantId).OrderByDescending(x => x.LastUpdate).ToPageListAsync(pageNumber, pageSize);
            }
            else
            {
                totalInvites = await Db.CompanyUserInvites
                    .Where(u => u.Status == "pending-finalization" && u.TenantId == tenantId).OrderByDescending(x => x.LastUpdate).ToPageListAsync(pageNumber, pageSize);
            }

            for (int i = 0; i < totalInvites.Items.Count(); i++)
            {
                totalInvites.Items[i].Status = "Invite Sent";
                totalInvites.Items[i].DateCreated = totalInvites.Items[i].DateCreated;
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                Data = totalInvites,
                ResponseMessage = "Pending Invitations",
            };
        }
        #endregion

        #region Get Suspended User Count
        public async Task<int> GetSuspendedUsersCount()
        {
            var suspendedUsers = await Db.UserProfiles.Where(u => u.IsSuspended == true).CountAsync();
            return suspendedUsers;
        }
        #endregion

        #region Get Suspended Users
        public async Task<Page<DisplayUserDto>> GetSuspendedUsers(PaginationParameters parameters, string subdomain)
        {
            var suspendedUserIds = await subdomainSchemaContext.SuspendedEmployees
                .Where(u => u.IsSuspended == true)
                .Select(x => x.UserId)
                .ToListAsync();

            var suspendedUsers = await subdomainSchemaContext.UserProfiles
            .Where(u => suspendedUserIds.Contains(u.UserId))
                .ToListAsync();

            var userDtoTasks = suspendedUsers.Select(async x => new DisplayUserDto()
            {
                UserProfileId = x.Id,
                UserId = x.UserId,
                ProfilePicture = x.ProfilePictureUrl != null & !string.IsNullOrWhiteSpace(x.ProfilePictureUrl) ? await _aWSS3Sevices.GetSignedUrlAsync(x.ProfilePictureUrl) : null,
                FirstName = x.FirstName,
                LastName = x.LastName,
                Role = GetUserRole(x.UserId, subdomain).Result,
                Email = x.Email,
                Status = x.IsSuspended == true ? "Suspended" : x.IsDeleted == true ? "Deleted" : "Active",
                LastSeen = CalculateLastSeen(x.UserId).Result,
                DateRegistered = x.DateCreated,
            }).ToList();

            // Await all async operations to complete
            var userDto = await Task.WhenAll(userDtoTasks);
            var result = userDto.OrderBy(x => x.FirstName).ThenBy(u => u.LastName).ToPageList(parameters.PageNumber, parameters.PageSize);

            return result;
        }
        #endregion

        #region Gets All Active Employees By Package
        public async Task<ApiResponse<IEnumerable<UserProfile>>> GetEmployeesByPackage(Applications application)
        {
            var employees = await subdomainSchemaContext.AppPermissions
                .Where(u => u.Application == application.ToString() && !u.IsSuspended).Select(x => x.UserId).ToListAsync();

            var employeesDeatils = await subdomainSchemaContext.UserProfiles.Where(u => employees.Contains(u.UserId)).ToListAsync();

            foreach (var employee in employeesDeatils)
            {
                employee.EmployeeRole = await GetUserRole(employee.UserId, subdomainSchemaContext.Schema);

                var suspendedEmployee = await subdomainSchemaContext.SuspendedEmployees.AnyAsync(u => u.UserId == employee.UserId && u.IsSuspended);
                if (suspendedEmployee)
                    employee.Status = "Suspended";
                else
                    employee.Status = "Active";
            }

            return new ApiResponse<IEnumerable<UserProfile>>
            {
                ResponseCode = "200",
                Data = employeesDeatils,
                ResponseMessage = "Employees retrived successfully",
            };
        }
        #endregion

        #region Get Or Search All Users
        public async Task<Page<DisplayUserDto>> GetAndSearchAllUsers(string subdomain, int pageNumber, int pageSize,
            string keyword = null, Applications? app = null, UserStatus? status = null)
        {
            var appPermissions = await subdomainSchemaContext.AppPermissions
                .Where(x => x.Application == app.ToString() && x.IsEnabled)
                .ToListAsync();

            var userIds = appPermissions.Select(x => x.UserId);

            var users = new List<UserProfile>();
            if (!string.IsNullOrEmpty(keyword))
            {
                users = await subdomainSchemaContext.UserProfiles
                    .Where(x => x.FirstName.ToLower().Contains(keyword.ToLower()) || x.LastName.ToLower().Contains(keyword.ToLower())
                     || x.MiddleName.ToLower().Contains(keyword.ToLower())).ToListAsync();
            }
            else
                users = await subdomainSchemaContext.UserProfiles.ToListAsync();
            if (users.Count == 0)
                return null;

            if (app is not null)
                users = users.Where(x => userIds.Contains(x.UserId)).ToList();

            var userDtoTasks = users.Select(x => new DisplayUserDto()
            {
                UserProfileId = x.Id,
                UserId = x.UserId,
                ProfilePicture = x.ProfilePictureUrl != null & !string.IsNullOrWhiteSpace(x.ProfilePictureUrl) ? _aWSS3Sevices.GetSignedUrlAsync(x.ProfilePictureUrl).Result : null,
                FirstName = x.FirstName,
                LastName = x.LastName,
                Role = GetUserRole(x.UserId, subdomain).Result,
                Email = x.Email,
                Status = x.IsSuspended == true ? UserStatus.Suspended.ToString() : x.IsDeleted == true ? UserStatus.Deleted.ToString() : UserStatus.Active.ToString(),
                LastSeen = CalculateLastSeen(x.UserId).Result,
                DateRegistered = x.DateCreated,
            }).ToList();

            // If app is not null, check users with full access to the app and make FullAccess field true
            if (app is not null)
            {
                foreach (var userDtoTask in userDtoTasks)
                {
                    userDtoTask.FullAccess = appPermissions
                        .Where(x => x.UserId == userDtoTask.UserId && x.SubscriptionStatus == Subscription.Enums.Enums.SubscriptionStatus.Active.ToString()).Any();

                    var hasPermission = await subdomainSchemaContext.AppPermissions
                            .FirstOrDefaultAsync(x => x.UserId == userDtoTask.UserId && x.Application == app.ToString());
                    if (hasPermission == null)
                    {
                        userDtoTask.Status = UserStatus.Inactive.ToString();
                    }
                }
            }

            if (status is not null)
                userDtoTasks = userDtoTasks.Where(x => x.Status == status.ToString()).ToList();

            var result = userDtoTasks.OrderBy(x => x.FirstName).ThenBy(u => u.LastName).ToPageList(pageNumber, pageSize);
            return result;
        }
        #endregion

        #region Calculate Last Seen
        private async Task<string> CalculateLastSeen(string userId)
        {
            var lastLoginDate = await Db.LoginLogs.Where(l => l.UserId == userId).OrderByDescending(l => l.LastLoginDate).Select(l => l.LastLoginDate).FirstOrDefaultAsync();
            TimeSpan timeSinceLogin = DateTime.UtcNow - lastLoginDate;

            if (timeSinceLogin.TotalSeconds < 60)
            {
                return "Just now";
            }
            else if (timeSinceLogin.TotalMinutes < 60)
            {
                int minutes = (int)timeSinceLogin.TotalMinutes;
                return $"{minutes} minute{(minutes != 1 ? "s" : "")} ago";
            }
            else if (timeSinceLogin.TotalHours < 24)
            {
                int hours = (int)timeSinceLogin.TotalHours;
                return $"{hours} hour{(hours != 1 ? "s" : "")} ago";
            }
            else if (timeSinceLogin.TotalDays < 7)
            {
                int days = (int)timeSinceLogin.TotalDays;
                return $"{days} day{(days != 1 ? "s" : "")} ago";
            }
            else if (timeSinceLogin.TotalDays < 30)
            {
                int weeks = (int)(timeSinceLogin.TotalDays / 7);
                return $"{weeks} week{(weeks != 1 ? "s" : "")} ago";
            }
            else if (timeSinceLogin.TotalDays < 365)
            {
                int months = (int)(timeSinceLogin.TotalDays / 30);
                return $"{months} month{(months != 1 ? "s" : "")} ago";
            }
            else
            {
                int years = (int)(timeSinceLogin.TotalDays / 365);
                return $"{years} year{(years != 1 ? "s" : "")} ago";
            }
        }
        #endregion

        #region Get Or Search All InActive Employees
        public async Task<Page<DisplayUserDto>> GetAllDeactivatedEmployees(int pageNumber, int pageSize, string subdomain, string keyword = null)
        {
            var users = new List<UserProfile>();
            if (!string.IsNullOrEmpty(keyword))
                users = await subdomainSchemaContext.UserProfiles
                    .Where(x => x.FirstName.ToLower().Contains(keyword.ToLower()) || x.LastName.ToLower().Contains(keyword.ToLower())
                     || x.MiddleName.ToLower().Contains(keyword.ToLower()) && (x.IsDeleted || x.IsSuspended)).Skip((pageNumber - 1) * pageSize).Take(pageSize).ToListAsync();
            else
                users = await subdomainSchemaContext.UserProfiles
                    .Where(x => x.IsDeleted || x.IsSuspended).Skip((pageNumber - 1) * pageSize).Take(pageSize).ToListAsync();
            if (users.Count == 0)
                return null;

            var userDto = users.Select(x => new DisplayUserDto()
            {
                UserProfileId = x.Id,
                UserId = x.UserId,
                ProfilePicture = x.ProfilePictureUrl,
                FirstName = x.FirstName,
                LastName = x.LastName,
                Role = GetUserRole(x.UserId, subdomain).Result,
                Email = x.Email,
                Status = x.IsSuspended == true ? "Suspended" : x.IsDeleted == true ? "Deleted" : "Active",
                LastSeen = CalculateLastSeen(x.UserId).Result,
                DateRegistered = x.DateCreated,
            }).ToList();

            var result = userDto.ToPageList(pageNumber, pageSize);
            return result;
        }
        #endregion

        #region Get User By Id
        public async Task<EmployeeInformation> ViewUserById(string userId, string subdomain)
        {
            var result = new EmployeeInformation();

            var reg_no = "";
            var workspace = "";
            var companyAddress = "";
            var tenant = await Db.Tenants.FirstOrDefaultAsync(x => x.Subdomain == subdomain);
            var user = await Db.Users.FirstOrDefaultAsync(u => u.Id == userId) ?? throw new RecordNotFoundException("User does not exist");
            var userProfile = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(u => u.UserId == userId);
            if (userProfile == null)
                throw new RecordNotFoundException("User profile does not exist on this company");

            if (tenant != null)
            {
                reg_no = tenant.RegNumber;
                workspace = tenant.WorkSpace;
                companyAddress = tenant.CompanyAddress;
            }

            result = new EmployeeInformation()
            {
                UserId = userId,
                FirstName = userProfile?.FirstName,
                LastName = userProfile?.LastName,
                MiddleName = userProfile?.MiddleName,
                Email = userProfile?.Email,
                Role = GetUserRole(userId, subdomain).Result,
                Status = userProfile?.IsSuspended == true ? "Suspended" : userProfile?.IsDeleted == true ? "Deleted" : "Active",
                Country = userProfile?.Country,
                PhoneNo = userProfile?.PhoneNumber,
                JobTitle = "",
                isTwoFactorEnabled = user.TwoFactorEnabled,
                TenantBusinessRegistrationNumber = reg_no,
                TenantWorkSpace = workspace,
                TenantCompanyAddress = companyAddress,
            };

            return result;

        }
        #endregion

        #region Suspend Employee
        public async Task<bool> SuspendEmployee(SuspendUser suspendDto)
        {
            var isSave = false;
            var userProfile = await subdomainSchemaContext.UserProfiles
                .FirstOrDefaultAsync(u => u.UserId == suspendDto.UserId) ?? throw new RecordNotFoundException("user not found");
            var suspendEmployee = await subdomainSchemaContext.SuspendedEmployees
                .FirstOrDefaultAsync(s => s.UserId == suspendDto.UserId);
            if (suspendEmployee == null)
            {
                var suspend = new SuspendedEmployee
                {
                    UserId = suspendDto.UserId,
                    UserProfileId = userProfile.Id,
                    StartDate = suspendDto.StartDate,
                    EndDate = suspendDto.EndDate,
                    IsIndefinite = suspendDto.IsIndefinite,
                    Message = suspendDto.Message,
                    IsSuspended = true
                };

                await subdomainSchemaContext.SuspendedEmployees.AddAsync(suspend);
            }
            else
            {
                suspendEmployee.StartDate = suspendDto.StartDate;
                suspendEmployee.EndDate = suspendDto.EndDate;
                suspendEmployee.IsIndefinite = suspendDto.IsIndefinite;
                suspendEmployee.Message = suspendDto.Message;
                suspendEmployee.IsSuspended = true;

                userProfile.IsSuspended = true;

                subdomainSchemaContext.Update(suspendEmployee);
                subdomainSchemaContext.UserProfiles.Update(userProfile);
            }

            var userAppPermissions = await subdomainSchemaContext.AppPermissions.Where(u => u.UserId == suspendDto.UserId)
                   .ToListAsync();
            userAppPermissions.ForEach(u => u.IsSuspended = true);
            userProfile.IsSuspended = true;
            subdomainSchemaContext.AppPermissions.UpdateRange(userAppPermissions);
            subdomainSchemaContext.UserProfiles.Update(userProfile);

            isSave = await subdomainSchemaContext.SaveChangesAsync() > 0;
            if (!isSave)
                return false;

            // Send an email notification to the user
            var duration = suspendDto.IsIndefinite ? "Indefinitely" : $"{suspendDto.StartDate.ToShortDateString()} to {suspendDto.EndDate.ToShortDateString()}";
            var parameters = new Dictionary<string, string>
            {
                { "{employee}", userProfile.FirstName + "" + userProfile.LastName },
                { "{duration}", duration }
            };

            var template = Extensions.UpdateTemplateWithParams("suspend-employee", _environment, parameters);
            var subject = "Suspension Notice";
            await _emailService.SendEmail(template, userProfile.Email, subject);

            // Log Activity
            var res = await LogActivity(_userId, $"suspended Employee [{userProfile.FirstName}]", "Employee suspended", userProfile.Id.ToString());
            if (!res)
                WatchLogger.LogError("Failed to log activity", "AdminService", "SuspendEmployee", "Failed to log activity");

            return isSave;
        }
        #endregion

        #region Unsuspend Employee
        public async Task<bool> UnSuspendEmployee(string userId)
        {
            var suspendedEmployee = await subdomainSchemaContext.SuspendedEmployees
               .Where(s => s.IsSuspended == true && s.UserId == userId).FirstOrDefaultAsync();
            if (suspendedEmployee == null)
                throw new InvalidOperationException("User is not suspended");

            suspendedEmployee.UnSuspendedOn = DateTime.UtcNow;
            suspendedEmployee.IsSuspended = false;
            subdomainSchemaContext.SuspendedEmployees.Update(suspendedEmployee);

            var profile = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(u => u.Id == suspendedEmployee.UserProfileId);
            profile.IsSuspended = false;

            var userAppPermissions = await subdomainSchemaContext.AppPermissions.Where(u => u.UserId == profile.UserId)
                    .ToListAsync();
            userAppPermissions.ForEach(u => u.IsSuspended = false);
            subdomainSchemaContext.AppPermissions.UpdateRange(userAppPermissions);

            var isSave = await subdomainSchemaContext.SaveChangesAsync() > 0;

            if (isSave)
            {
                // TODO: Send mail notification

                // Log Activity
                var res = await LogActivity(_userId, "Employee suspension lifted", "Employee suspension lifted", null);
                if (!res)
                    WatchLogger.LogError("Failed to log activity", "AdminService", "UnSuspendDueEmployees", "Failed to log activity");

                return true;
            }

            return false;
        }
        #endregion

        #region Delete User
        public async Task<bool> DeleteUser(string userId)
        {
            var result = false;

            var userProfile = await Db.UserProfiles.FirstOrDefaultAsync(u => u.UserId == userId);
            if (userProfile == null) throw new RecordNotFoundException("user profile not found");

            userProfile.IsDeleted = true;
            userProfile.UserLoggedOut = true;
            await Db.SaveChangesAsync();

            var appPermision = await Db.AppPermissions.Where(a => a.UserId == userId).ToListAsync();
            foreach (var permission in appPermision)
            {
                permission.IsEnabled = false;
                permission.SubscriptionStatus = Subscription.Enums.Enums.SubscriptionStatus.Inactive.ToString();
            }

            result = await Db.SaveChangesAsync() > 0;

            // Push UserDeleted NOTIFICATION via signal R
            await _hubContext.Clients.User(userId)
                .SendAsync("UserDeleted", userId);

            // Log Activity
            var res = await LogActivity(_userId, "Employee deleted", "Employee deleted", userProfile.Id.ToString());
            if (!res)
                WatchLogger.LogError("Failed to log activity", "AdminService", "DeleteUser", "Failed to log activity");

            return result;
        }
        #endregion

        #region Change User Role
        public async Task<bool> ChangeRole(string userId, string roleId, string appName)
        {
            var user = await Db.UserAndRoleIds.FirstOrDefaultAsync(u => u.UserProId == userId) ?? throw new RecordNotFoundException("user not found");
            user.RoleId = roleId;
            Db.Update(user);

            var result = await Db.SaveChangesAsync() > 0;

            // Push UserDeleted NOTIFICATION via signal R
            await _hubContext.Clients.User(userId)
                .SendAsync("UserRoleUpdated", userId);

            // Log Activity
            var res = await LogActivity(_userId, "Employee role changed", "Employee role changed", user.Id.ToString());
            if (!res)
                WatchLogger.LogError("Failed to log activity", "AdminService", "ChangeRole", "Failed to log activity");

            return result;
        }
        #endregion

        #region Get packages the user has access to
        public async Task<Dictionary<string, bool>> GetPackagesWithUserPermissions(string userId)
        {
            var result = new Dictionary<string, bool>();
            var userAppPermission = await subdomainSchemaContext.AppPermissions.Where(a => a.UserId == userId).ToDictionaryAsync(a => a.Application, a => a.IsEnabled);
            foreach (var package in Enum.GetNames(typeof(Applications)))
            {
                if (userAppPermission.TryGetValue(package, out var isEnabled))
                {
                    result[package] = isEnabled;
                }
                else
                {
                    result[package] = false;
                }
            }
            return result;
        }
        #endregion

        #region Remove User App Permission
        public async Task<bool> RemoveUserAppPermission(string userId, Applications appPermission)
        {
            var userAppPermission = await Db.AppPermissions.Where(a => a.UserId == userId && a.Application == appPermission.ToString()).FirstOrDefaultAsync();
            userAppPermission.IsEnabled = false;
            var result = await Db.SaveChangesAsync() > 0;

            // Log Activity
            var res = await LogActivity(_userId, "User permission removed", "User permission removed", userAppPermission.Id.ToString());
            if (!res)
                WatchLogger.LogError("Failed to log activity", "AdminService", "RemoveUserAppPermission", "Failed to log activity");

            return result;
        }
        #endregion

        #region Get Roles For An App - eg Joble
        public async Task<List<RolesDto>> GetEmployeeRoles(Applications appName)
        {
            var roles = await subdomainSchemaContext.EmployeeRoles
                .Where(x => x.PackageName == appName.ToString()).Select(x => new RolesDto
                {
                    RoleId = x.Id,
                    RoleName = x.RoleName,
                    PackageName = x.PackageName,
                }).ToListAsync();
            return roles;
        }
        #endregion

        #region Get Employees that are assigned to a role
        public async Task<List<RolesUsers>> GetRoleUsers(string roleId)
        {
            var role = await subdomainSchemaContext.EmployeeRoles.FirstOrDefaultAsync(x => x.Id == roleId);
            var roleUserIds = await subdomainSchemaContext.UserAndRoleIds.Where(u => u.RoleId == roleId).Select(x => x.UserProId).ToListAsync();
            var users = await subdomainSchemaContext.UserProfiles.Where(x => roleUserIds.Contains(x.UserId)).Select(x => new RolesUsers
            {
                RoleName = role.RoleName,
                FirstName = x.FirstName,
                LastName = x.LastName,
                UsersEmail = x.Email,
            }).ToListAsync();
            return users;
        }
        #endregion

        #region Get All Employee Permissions
        public async Task<List<PermissionDto>> GetEmployeeRolesAndPermission(string roleId)
        {
            var allPermissions = await subdomainSchemaContext.EmployeePermissions
                .Select(permission => new PermissionDto
                {
                    PermissionId = permission.Id,
                    PermissionCategory = permission.PermissionCategory,
                    PermissionName = CurrentCulture.TextInfo.ToTitleCase(permission.PermissionName.Replace("_", " ").ToLower().Trim())
                }).ToListAsync();

            if (!string.IsNullOrEmpty(roleId))
            {
                var rolePermissions = await subdomainSchemaContext.EmployeeRolesPermissions
                    .Where(rp => rp.RoleId == roleId)
                    .Select(rp => rp.EmployeePermission.Id)
                    .ToListAsync();
                foreach (var permission in allPermissions)
                {
                    permission.IsAssociatedWithRole = rolePermissions.Contains(permission.PermissionId);
                }
            }

            return allPermissions;
        }
        #endregion

        #region Assign User To A Role
        public async Task<ApiResponse<bool>> AssignUserToRole(string userId, string roleId)
        {
            var role = await subdomainSchemaContext.EmployeeRoles.FirstOrDefaultAsync(role => role.Id == roleId);
            if (role == null)
                throw new RecordNotFoundException("Role does not exixt");

            var user = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(user => user.UserId == userId);
            if (user == null)
                throw new RecordNotFoundException("User not found");

            // Check if the user has already been assigned to a role
            var userRole = await subdomainSchemaContext.UserAndRoleIds.FirstOrDefaultAsync(userRole => userRole.UserProId == userId);
            if (userRole != null)
            {
                userRole.RoleId = roleId;
                subdomainSchemaContext.UserAndRoleIds.Update(userRole);
            }
            else
            {
                var userIdRoleId = new UserAndRoleId
                {
                    UserProId = userId,
                    RoleId = roleId
                };

                await subdomainSchemaContext.UserAndRoleIds.AddAsync(userIdRoleId);
            }

            var dbResult = await subdomainSchemaContext.SaveChangesAsync();


            if (dbResult > 0)
            {
                // Log Activity
                var res = await LogActivity(_userId, "Role assigned", "Role assigned", userId);
                if (!res)
                    WatchLogger.LogError("Failed to log activity", "AdminService", "AssignUserToRole", "Failed to log activity");
            }

            return new ApiResponse<bool>
            {
                ResponseCode = dbResult > 0 ? "200" : "500",
                ResponseMessage = dbResult > 0 ? "Role assigned successfully" : "Role assignment failed",
                Data = dbResult > 0 ? true : false
            };
        }
        #endregion

        #region Assign Permissions To A Role
        public async Task<ApiResponse<bool>> AssignPermissionsToRole(string roleId, List<string> permissionIds)
        {
            var role = await subdomainSchemaContext.EmployeeRoles.FirstOrDefaultAsync(role => role.Id == roleId);
            if (role == null)
                throw new RecordNotFoundException("Rold does not exixt");

            var permissions = await subdomainSchemaContext.EmployeePermissions.Where(permission => permissionIds.Contains(permission.Id)).ToListAsync();
            if (permissions.Count != permissionIds.Count)
                throw new RecordNotFoundException("One or more selected permission does not exist");

            var rolePermissions = new List<EmployeeRolesPermission>();
            foreach (var permission in permissions)
            {
                rolePermissions.Add(new EmployeeRolesPermission
                {
                    RoleId = roleId,
                    PermissionId = permission.Id
                });
            }

            await subdomainSchemaContext.EmployeeRolesPermissions.AddRangeAsync(rolePermissions);
            var dbResult = await subdomainSchemaContext.SaveChangesAsync();

            if (dbResult > 0)
            {
                // Log Activity
                var res = await LogActivity(_userId, "Permissions assigned", "Permissions assigned", roleId);
                if (!res)
                    WatchLogger.LogError("Failed to log activity", "AdminService", "AssignPermissionsToRole", "Failed to log activity");
            }

            return new ApiResponse<bool>
            {
                ResponseCode = dbResult > 0 ? "200" : "500",
                ResponseMessage = dbResult > 0 ? "Permissions assigned successfully" : "Permissions assignment failed",
                Data = dbResult > 0 ? true : false
            };
        }
        #endregion

        #region Update Role
        public async Task<bool> UpdateRole(UpdateRoleDto roleDto)
        {
            var result = false;
            try
            {
                var role = await subdomainSchemaContext.EmployeeRoles.FirstOrDefaultAsync(u => u.Id == u.Id);
                if (role != null)
                {
                    role.RoleName = roleDto.NewName;
                    subdomainSchemaContext.EmployeeRoles.Update(role);
                    result = await subdomainSchemaContext.SaveChangesAsync() > 0;

                    // Log Activity
                    var res = await LogActivity(_userId, "Role updated", "Role updated", role.Id);
                    if (!res)
                        WatchLogger.LogError("Failed to log activity", "AdminService", "UpdateRole", "Failed to log activity");

                    return result;
                }

                throw new RecordNotFoundException("role does not exist");
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion

        #region Delete Roles
        public async Task DeleteRole(List<string> roleIds)
        {
            foreach (var roleId in roleIds)
            {
                var role = await subdomainSchemaContext.EmployeeRoles.FirstOrDefaultAsync(d => d.Id == roleId) ?? throw new RecordNotFoundException("One or more of the selected roles does not exist");

                subdomainSchemaContext.EmployeeRoles.Remove(role);
            }

            var result = await subdomainSchemaContext.SaveChangesAsync();
            if (result <= 0)
                throw new OperationFailedException("Role deletion failed");

            // Log Activity
            var res = await LogActivity(_userId, "Role deleted", "Role deleted", string.Join(",", roleIds));
            if (!res)
                WatchLogger.LogError("Failed to log activity", "AdminService", "DeleteRole", "Failed to log activity");
        }
        #endregion

        #region Create Role
        public async Task<bool> CreateRoles(string roleName, string appName)
        {
            var roles = await subdomainSchemaContext.EmployeeRoles.FirstOrDefaultAsync(r => r.RoleName == roleName && r.PackageName == appName);
            if (roles != null) throw new Exception("Role already exist");
            var newRole = new EmployeeRoles { RoleName = roleName, PackageName = appName };
            await subdomainSchemaContext.EmployeeRoles.AddAsync(newRole);

            var result = await subdomainSchemaContext.SaveChangesAsync() > 0;

            // Log Activity
            var res = await LogActivity(_userId, "Role created", "Role created", newRole.Id);
            if (!res)
                WatchLogger.LogError("Failed to log activity", "AdminService", "CreateRoles", "Failed to log activity");

            return result;
        }
        #endregion

        #region Get activity logs
        public async Task<Page<Activity>> GetActivityLogs(int pageNumber, int pageSize, DateTime sDate, DateTime eDate)
        {
            var activities = await subdomainSchemaContext.Activities
                .Where(x => x.CreatedAt >= sDate && x.CreatedAt <= eDate && x.EventCategory == EventCategory.ClientAdmin)
                .OrderByDescending(x => x.CreatedAt).ToPageListAsync(pageNumber, pageSize);
            if (activities.TotalSize == 0) { return null; }

            return activities;
        }
        #endregion

        #region Create Permission - Not Needed
        public async Task<bool> CreatePermissions(List<CreatePermission> permission)
        {
            foreach (var permissionItem in permission)
            {
                var checkPermission = await Db.EmployeePermissions.FirstOrDefaultAsync(r => r.PermissionName == permissionItem.PermissionName);
                if (checkPermission != null) throw new Exception("Permission already exist");
                var newPermission = new EmployeePermission { PermissionName = permissionItem.PermissionName, PermissionCategory = permissionItem.Category };
                await Db.EmployeePermissions.AddAsync(newPermission);
            }
            var result = await Db.SaveChangesAsync() > 0;
            return result;
        }
        #endregion

        #region Create Log
        /// <summary>
        /// Create Log
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<bool> CreateLog(ActivityDto model)
        {
            var log = new Activity()
            {
                Description = model.Description,
                ActivitySummary = model.ActivitySummary,
                EventId = model.EventId,
                EventCategory = model.EventCategory,
                UserId = model.UserId,
                By = model.By,
                Application = model.Application,
                CreatedAt = DateTime.UtcNow,
                GenericUrl = model.GenericUrl
            };

            subdomainSchemaContext.Activities.Add(log);
            var result = await subdomainSchemaContext.SaveChangesAsync();

            return result > 0;
        }
        #endregion

        // Deletion Section //

        #region Request For Company Deletion
        public async Task<GenericResponse> RequestForCompanyDeletion(RequestForCompanyDeletionDto model)
        {
            // Check if the company have an existing request
            var existingRequest = await Db.CompanyDeletionRequests
                .FirstOrDefaultAsync(x => x.TenantId == model.TenantId && x.Status == AccountDeletionStatus.Pending);
            if (existingRequest != null)
                return new GenericResponse { ResponseCode = "400", DevResponseMessage = "Request already exists", ResponseMessage = "You have an existing request. Please wait for it to be processed." };

            var tenant = await Db.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == model.TenantId);
            if (tenant == null)
                return new GenericResponse { ResponseCode = "400", DevResponseMessage = "Tenant not found", ResponseMessage = "Something went wrong, please try again later" };

            var loggedInUser = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.UserId == model.RequestedBy); if (loggedInUser == null)
                return new GenericResponse { ResponseCode = "400", DevResponseMessage = "User not found", ResponseMessage = "Something went wrong, please try again later" };

            var reqId = Utility.RandomString(15);
            var request = new CompanyDeletionRequest
            {
                Reason = model.Reason,
                RequestedBy = model.RequestedBy,
                RequestId = reqId,
                TenantId = model.TenantId,
            };
            await Db.CompanyDeletionRequests.AddAsync(request);
            var result = await Db.SaveChangesAsync() > 0;

            if (result)
            {
                // TODO: Call super admin create ticket endpoint to create a ticket
                var fullName = loggedInUser.FirstName + " " + loggedInUser.LastName;
                var accountDeletionReqMessage = BuildDeletionMessage(loggedInUser.Email, tenant.CompanyName, fullName);
                var payload = new ExternalCreateTicket
                {
                    CustomerEmail = loggedInUser.Email,
                    CustomerName = loggedInUser.FirstName + " " + loggedInUser.LastName,
                    CategoryId = Guid.Parse(_configuration.GetValue<string>("AccountClosureTicketCategoryId")),
                    Subject = "Account Deletion Request",
                    Message = accountDeletionReqMessage,
                };

                var ticketNo = "";
                var url = _appSettings.SuperAdminEnpoints.BaseUrl + _appSettings.SuperAdminEnpoints.CreateTicketExternal;
                var apiResponse = await _apiCallService.MakeApiCallGenericAsync<ExternalCreateTicket, ExternalCreateTicketResponse>(_appSettings.SuperAdminEnpoints.BaseUrl, _appSettings.SuperAdminEnpoints.CreateTicketExternal, Method.Post, payload, null);
                if (apiResponse != null && apiResponse.ResponseCode == "200")
                {
                    ticketNo = apiResponse.Data.ReferenceId;
                }
                else
                {
                    // Delete the request if ticket creation fails
                    Db.CompanyDeletionRequests.Remove(request);
                    await Db.SaveChangesAsync();

                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        DevResponseMessage = "Ticket could not be created",
                        ResponseMessage = "Something went wrong, please try again later"
                    };
                }

                // Get admin details
                var admin = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.UserId == tenant.AdminId);

                // Send an email notification to the user
                var parameters = new Dictionary<string, string>
                 {
                     { "{companyName}", tenant.CompanyName.CapitalizeFirstLetterOfEachWord() },
                     { "{requestId}", reqId },
                     { "{requestDate}", DateTime.UtcNow.ToShortDateString() },
                     { "{reason}", model.Reason },
                     { "{name}", loggedInUser.FirstName + " " + loggedInUser.LastName },
                     { "{cancelLink}", Utility.Constants.ADMIN_URL },
                     { "{ticketNo}", ticketNo }
                 };

                var template = Extensions.UpdateTemplateWithParams("company_deletion_received", _environment, parameters);
                var subject = "Account Deletion Request Confirmed";
                await _emailService.SendEmail(template, loggedInUser.Email, subject);
                if (loggedInUser.Email.ToLower() != admin.Email.ToLower())
                    await _emailService.SendEmail(template, admin.Email, subject); // Send to admin as well

                // Send an email notification to the admin
                var templateAdmin = Extensions.UpdateTemplateWithParams("company_deletion_req", _environment, parameters);
                var subjectAdmin = "Account Deletion Request";
                await _emailService.SendEmail(templateAdmin, "<EMAIL>", subjectAdmin);
                await _emailService.SendEmail(templateAdmin, "<EMAIL>", subjectAdmin);

                return new GenericResponse { ResponseCode = "200", ResponseMessage = "Your request for company deletion has been received. We will get back to you shortly." };
            }

            return new GenericResponse { ResponseCode = "400", DevResponseMessage = "Request not saved", ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE };
        }
        #endregion

        #region Cancel Company Deletion
        public async Task<GenericResponse> CancelCompanyDeletion(string requestId)
        {
            var request = await Db.CompanyDeletionRequests.FirstOrDefaultAsync(x => x.RequestId == requestId);
            if (request == null)
                return new GenericResponse { ResponseCode = "400", DevResponseMessage = "Request not found", ResponseMessage = "Something went wrong, please try again later" };

            request.Status = AccountDeletionStatus.Cancelled;
            request.UpdatedOn = DateTime.UtcNow;
            Db.CompanyDeletionRequests.Update(request);
            var result = await Db.SaveChangesAsync() > 0;

            if (result)
            {
                return new GenericResponse { ResponseCode = "200", ResponseMessage = "Request for company deletion has been cancelled." };
            }

            return new GenericResponse { ResponseCode = "400", DevResponseMessage = "Request not saved", ResponseMessage = "Something went wrong, please try again later" };
        }
        #endregion

        #region Get Company Deletion Requests
        public async Task<GenericResponse> GetCompanyDeletionRequests(string tenantId)
        {
            var requests = await Db.CompanyDeletionRequests.Where(x => x.TenantId == tenantId && x.Status == AccountDeletionStatus.Pending).ToListAsync();
            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Company deletion request retrieved successfully.",
                Data = requests
            };
        }
        #endregion

        // Localization Section //

        #region Add Or Update Localization
        public async Task<GenericResponse> AddOrUpdateLocalization(AddOrUpdateLocalizationDto model)
        {
            var existingLocalization = await subdomainSchemaContext.Localizations.FirstOrDefaultAsync(x => x.UserId == model.UserId);
            if (existingLocalization != null)
            {
                existingLocalization.LanguagePreference = model.LanguagePreference;
                existingLocalization.DateFormat = model.DateFormat;
                existingLocalization.TimeFormat = model.TimeFormat;
                existingLocalization.TimeZone = model.TimeZone;
                existingLocalization.Currency = model.Currency;
                existingLocalization.CalenderWeekStartsOn = model.CalenderWeekStartsOn;
                existingLocalization.AmountFormat = model.AmountFormat;
                existingLocalization.UpdatedAt = DateTime.UtcNow;
                existingLocalization.UpdatedBy = _userId;
                existingLocalization.UserId = model.UserId;
                subdomainSchemaContext.Localizations.Update(existingLocalization);
            }
            else
            {
                var newLocalization = _mapper.Map<Localization>(model);
                newLocalization.CreatedBy = _userId;
                await subdomainSchemaContext.Localizations.AddAsync(newLocalization);
            }

            var result = await subdomainSchemaContext.SaveChangesAsync() > 0;
            if (result)
            {
                // Log Activity
                var res = await LogActivity(_userId, "Localization added/updated", "Localization added/updated", null);
                if (!res)
                    WatchLogger.LogError("Failed to log activity", "AdminService", "AddOrUpdateLocalization", "Failed to log activity");

                return new GenericResponse { ResponseCode = "200", ResponseMessage = "Localization added/updated successfully." };
            }
            return new GenericResponse { ResponseCode = "400", ResponseMessage = "Failed to add/update localization" };
        }
        #endregion

        #region Get Localization
        public async Task<GenericResponse> GetLocalization(string userId = null)
        {
            var localization = await subdomainSchemaContext.Localizations.FirstOrDefaultAsync(x => x.UserId == userId);
            return new GenericResponse { ResponseCode = "200", ResponseMessage = "Localization retrieved successfully.", Data = localization };
        }
        #endregion

        // Password Policy //

        #region Add Or Update Password Policy
        public async Task<GenericResponse> AddOrUpdatePasswordPolicy(PasswordPolicyDto model)
        {
            var passwordPolicy = await subdomainSchemaContext.PasswordPolicies.FirstOrDefaultAsync();
            if (passwordPolicy == null)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "Password policy not found" };

            if (passwordPolicy != null)
            {
                passwordPolicy.MinimumPasswordLength = model.MinimumPasswordLength;
                passwordPolicy.RequirePasswordExpiration = model.RequirePasswordExpiration;
                passwordPolicy.PasswordExpirationDays = model.PasswordExpirationDays;
                passwordPolicy.RequireAtLeastOneLowercase = model.RequireAtLeastOneLowercase;
                passwordPolicy.RequireAtLeastOneUppercase = model.RequireAtLeastOneUppercase;
                passwordPolicy.RequireAtLeastOneNumber = model.RequireAtLeastOneNumber;
                passwordPolicy.RequireAtLeastOneSpecialCharacter = model.RequireAtLeastOneSpecialCharacter;
                passwordPolicy.ProhibitUserNameAsPassword = model.ProhibitUserNameAsPassword;
                passwordPolicy.UpdatedBy = _userId;
                passwordPolicy.UpdatedAt = DateTime.UtcNow;

                subdomainSchemaContext.PasswordPolicies.Update(passwordPolicy);
            }
            else
            {
                var newPasswordPolicy = _mapper.Map<PasswordPolicy>(model);
                newPasswordPolicy.CreatedBy = _userId;
                await subdomainSchemaContext.PasswordPolicies.AddAsync(newPasswordPolicy);
            }

            var result = await subdomainSchemaContext.SaveChangesAsync() > 0;
            if (result)
            {
                // Log Activity
                var res = await LogActivity(_userId, "Password policy updated", "Password policy updated", null);
                if (!res)
                    WatchLogger.LogError("Failed to log activity", "AdminService", "UpdatePasswordPolicy", "Failed to log activity");

                return new GenericResponse { ResponseCode = "200", ResponseMessage = "Password policy updated successfully." };
            }

            return new GenericResponse { ResponseCode = "400", ResponseMessage = "Failed to update password policy" };
        }
        #endregion

        #region Get Password Policy
        public async Task<GenericResponse> GetPasswordPolicy()
        {
            var passwordPolicy = await subdomainSchemaContext.PasswordPolicies.FirstOrDefaultAsync();
            if (passwordPolicy == null)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "Password policy not found" };

            return new GenericResponse { ResponseCode = "200", ResponseMessage = "Password policy retrieved successfully.", Data = passwordPolicy };
        }
        #endregion

        // 2FA Settings //

        #region Add Or Update 2FA Settings
        public async Task<GenericResponse> AddOrUpdateTwoFactorSettings(TwoFactorSettingsDto model)
        {
            var twoFactorSettings = await subdomainSchemaContext.TwoFactorSettings.FirstOrDefaultAsync();
            if (model.options == _2FAOptions.None)
            {
                model.Require2FA = false;
                model.Require2FAOnThirdLogin = false;
                model.Require2FAOnSecondFailedLoginAttempt = false;
                model.ImidiatelyRequire2FA = false;
            }

            if (twoFactorSettings != null)
            {
                twoFactorSettings.options = model.options;
                twoFactorSettings.Require2FA = model.Require2FA;
                twoFactorSettings.Require2FAOnThirdLogin = model.Require2FAOnThirdLogin;
                twoFactorSettings.Require2FAOnSecondFailedLoginAttempt = model.Require2FAOnSecondFailedLoginAttempt;
                twoFactorSettings.UpdatedBy = _userId;
                twoFactorSettings.ImidiatelyRequire2FA = model.ImidiatelyRequire2FA;
                twoFactorSettings.UpdatedAt = DateTime.UtcNow;

                subdomainSchemaContext.TwoFactorSettings.Update(twoFactorSettings);
            }
            else
            {
                var newTwoFactorSettings = _mapper.Map<TwoFactorSetting>(model);
                newTwoFactorSettings.CreatedBy = _userId;
                await subdomainSchemaContext.TwoFactorSettings.AddAsync(newTwoFactorSettings);
            }

            var result = await subdomainSchemaContext.SaveChangesAsync() > 0;
            if (result)
            {
                // Log Activity
                var res = await LogActivity(_userId, "2FA settings updated", "2FA settings updated", null);
                if (!res)
                    WatchLogger.LogError("Failed to log activity", "AdminService", "UpdateTwoFactorSettings", "Failed to log activity");

                // Log all users out
                var logoutResult = await LogAllUsersOut();

                return new GenericResponse { ResponseCode = "200", ResponseMessage = "2FA settings updated successfully." };
            }

            return new GenericResponse { ResponseCode = "400", ResponseMessage = "Failed to update 2FA settings" };
        }
        #endregion

        #region Get 2FA Settings
        public async Task<GenericResponse> GetTwoFactorSettings()
        {
            var twoFactorSettings = await subdomainSchemaContext.TwoFactorSettings.FirstOrDefaultAsync();
            if (twoFactorSettings == null)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "Two factor settings not found" };

            return new GenericResponse { ResponseCode = "200", ResponseMessage = "Two factor settings retrieved successfully.", Data = twoFactorSettings };
        }
        #endregion

        // Others //

        #region Log All Users Out
        public async Task<GenericResponse> LogAllUsersOut()
        {
            var users = await subdomainSchemaContext.UserProfiles.ToListAsync();
            foreach (var user in users)
            {
                user.UserLoggedOut = true;
            }

            var result = await subdomainSchemaContext.SaveChangesAsync() > 0;

            // Log Activity
            var res = await LogActivity(_userId, "All users logged out", "All users logged out", null);
            if (!res)
                WatchLogger.LogError("Failed to log activity", "AdminService", "LogAllUsersOut", "Failed to log activity");

            return new GenericResponse
            {
                ResponseCode = result ? "200" : "500",
                ResponseMessage = result ? "Success" : "Failed"
            };
        }
        #endregion

        #region Check if the user has granted permission for his/her activity to be logged
        private async Task<bool> CheckIfUserHasGrantedPermission(string userId, EventCategory eventCategory)
        {
            var user = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (!user.LogActivity)
                return false;

            var categories = user.EventCategory;
            if (categories.Contains(EventCategory.All.ToString()) || categories.Contains(eventCategory.ToString()))
                return true;

            return false;
        }
        #endregion

        #region Update employee info
        public async Task<GenericResponse> UpdateEmployee(EmployeeUpdate updateDto)
        {
            var result = false;
            var userProfile = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(u => u.UserId == updateDto.UserId);
            if (userProfile == null)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "user not found" };

            if (!Enum.IsDefined(typeof(Applications), updateDto.Package))
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "package not found" };

            var role = await subdomainSchemaContext.EmployeeRoles.FirstOrDefaultAsync(u => u.Id == updateDto.RoleId && u.PackageName == updateDto.Package.ToString());
            if (role == null)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "role does not exist" };

            var user = await subdomainSchemaContext.UserAndRoleIds.FirstOrDefaultAsync(u => u.UserProId == updateDto.UserId);
            if (user == null)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "user not found" };

            user.RoleId = updateDto.RoleId;

            if (!string.IsNullOrEmpty(updateDto.PhoneNumber))
                userProfile.PhoneNumber = updateDto.PhoneNumber;

            userProfile.EmployeeRole = updateDto.RoleId;
            userProfile.FirstName = updateDto.FirstName;
            userProfile.LastName = updateDto.LastName;
            userProfile.LastUpdate = DateTime.UtcNow;

            result = await subdomainSchemaContext.SaveChangesAsync() > 0;
            if (result)
            {
                // Log Activity
                var res = await LogActivity(_userId, $"Edit Employee [{userProfile.FirstName}]", "Employee edited", userProfile.Id.ToString());
                if (!res)
                    WatchLogger.LogError("Failed to log activity", "AdminService", "UpdateEmployee", "Failed to log activity");
                return new GenericResponse { ResponseCode = "200", ResponseMessage = "Employee details updated successfully." };
            }

            return new GenericResponse { ResponseCode = "400", ResponseMessage = "Failed to update employee details" }; ;
        }

        #endregion

        // Billing INFO

        #region Billing Information Management

        /// <summary>
        /// Add billing information for a company
        /// </summary>
        /// <param name="model">Billing information data</param>
        /// <param name="subdomain">Company subdomain</param>
        /// <param name="userId">User ID creating the record</param>
        /// <returns></returns>
        public async Task<GenericResponse> AddBillingInformation(AddBillingInformationDto model, string subdomain, string userId)
        {
            // Get tenant information
            var tenant = await Db.Tenants.FirstOrDefaultAsync(x => x.Subdomain.ToLower() == subdomain.ToLower());
            if (tenant == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Company not found"
                };
            }

            // Validate email domain matches company domain
            var emailDomain = model.Email.Split('@')[1].ToLower();
            var companyDomain = tenant.VerifiedEmailDomain.ToLower();

            if (emailDomain != companyDomain)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Email domain must match company domain: {companyDomain}"
                };
            }

            // Check if billing information already exists
            var existingBilling = await Db.BillingInformations
                .FirstOrDefaultAsync(x => x.TenantId == tenant.Id);

            if (existingBilling != null)
            {
                return new GenericResponse
                {
                    ResponseCode = "409",
                    ResponseMessage = "Billing information already exists. Use update endpoint to modify."
                };
            }

            // Create new billing information using AutoMapper
            var billingInfo = _mapper.Map<BillingInformation>(model);
            billingInfo.TenantId = tenant.Id;
            billingInfo.CreatedBy = userId;
            billingInfo.UpdatedBy = userId;
            billingInfo.CreatedAt = DateTime.UtcNow;
            billingInfo.UpdatedAt = DateTime.UtcNow;

            Db.BillingInformations.Add(billingInfo);
            var result = await Db.SaveChangesAsync() > 0;

            if (result)
            {
                // Log Activity
                var res = await LogActivity(userId, "Billing information added", "Billing information created", billingInfo.Id.ToString());
                if (!res)
                    WatchLogger.LogError("Failed to log activity", "AdminService", "AddBillingInformation", "Failed to log activity");

                var responseDto = _mapper.Map<BillingInformationResponseDto>(billingInfo);

                return new GenericResponse
                {
                    ResponseCode = "201",
                    ResponseMessage = "Billing information added successfully",
                    Data = responseDto
                };
            }

            return new GenericResponse
            {
                ResponseCode = "500",
                ResponseMessage = "Failed to add billing information"
            };
        }

        /// <summary>
        /// Update billing information for a company
        /// </summary>
        /// <param name="model">Updated billing information data</param>
        /// <param name="subdomain">Company subdomain</param>
        /// <param name="userId">User ID updating the record</param>
        /// <returns></returns>
        public async Task<GenericResponse> UpdateBillingInformation(UpdateBillingInformationDto model, string subdomain, string userId)
        {
            // Get tenant information
            var tenant = await Db.Tenants.FirstOrDefaultAsync(x => x.Subdomain.ToLower() == subdomain.ToLower());
            if (tenant == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Company not found"
                };
            }

            // Validate email domain matches company domain
            var emailDomain = model.Email.Split('@')[1].ToLower();
            var companyDomain = tenant.VerifiedEmailDomain.ToLower();

            if (emailDomain != companyDomain)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Email domain must match company domain: {companyDomain}"
                };
            }

            // Get existing billing information
            var existingBilling = await Db.BillingInformations
                .FirstOrDefaultAsync(x => x.TenantId == tenant.Id);

            if (existingBilling == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Billing information not found. Use add endpoint to create."
                };
            }

            // Update billing information using AutoMapper
            _mapper.Map(model, existingBilling);
            existingBilling.UpdatedBy = userId;
            existingBilling.UpdatedAt = DateTime.UtcNow;

            Db.BillingInformations.Update(existingBilling);
            var result = await Db.SaveChangesAsync() > 0;

            if (result)
            {
                // Log Activity
                var res = await LogActivity(userId, "Billing information updated", "Billing information modified", existingBilling.Id.ToString());
                if (!res)
                    WatchLogger.LogError("Failed to log activity", "AdminService", "UpdateBillingInformation", "Failed to log activity");

                var responseDto = _mapper.Map<BillingInformationResponseDto>(existingBilling);

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Billing information updated successfully",
                    Data = responseDto
                };
            }

            return new GenericResponse
            {
                ResponseCode = "500",
                ResponseMessage = "Failed to update billing information"
            };
        }

        /// <summary>
        /// Get billing information for a company
        /// </summary>
        /// <param name="subdomain">Company subdomain</param>
        /// <returns></returns>
        public async Task<GenericResponse> GetBillingInformation(string subdomain)
        {
            var tenant = await Db.Tenants.FirstOrDefaultAsync(x => x.Subdomain.ToLower() == subdomain.ToLower());
            if (tenant == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Company not found"
                };
            }

            // Get billing information
            var billingInfo = await Db.BillingInformations
                .FirstOrDefaultAsync(x => x.TenantId == tenant.Id);

            if (billingInfo == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Billing information not found"
                };
            }

            var responseDto = _mapper.Map<BillingInformationResponseDto>(billingInfo);

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Billing information retrieved successfully",
                Data = responseDto
            };
        }

        #endregion

        // Private Methods

        #region Private Methods
        public static string BuildDeletionMessage(string email, string companyName, string fullName)
        {
            StringBuilder message = new StringBuilder();

            message.AppendLine("Subject: Request for Account Deletion\n");
            message.AppendLine("Dear Support Team,\n");
            message.AppendLine("I am writing to request the deletion of my account with [Service/Product Name]. Please find my account details below:\n");

            message.AppendLine($"- Username/Email: {email}");
            if (!string.IsNullOrEmpty(companyName))
            {
                message.AppendLine($"- Company Name: {companyName}");
            }

            message.AppendLine("\nKindly confirm the deletion of my account and any associated data.\n");
            message.AppendLine("Thank you for your assistance. I look forward to your prompt response.\n");

            message.AppendLine($"Best regards,");
            message.AppendLine($"{fullName}");

            return message.ToString();
        }

        /// <summary>
        /// Add data to redis - Background job
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="req"></param>
        /// <param name="redisKey"></param>
        /// <param name="totalTodos"></param>
        /// <returns></returns>
        private async Task AddDataToRedis<T>(string redisKey, T totalTodos)
        {
            var redisRes = await _redisCacheService.SetDataAsync(redisKey, totalTodos, DateTimeOffset.Now.AddDays(1));
            if (!redisRes)
                WatchLogger.LogError(nameof(AddDataToRedis), "Adding response to Redis failed after retries");
        }

        /// <summary>
        /// Log activity
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="desc"></param>
        /// <param name="summary"></param>
        /// <param name="eventId"></param>
        /// <returns></returns>
        private async Task<bool> LogActivity(string userId, string desc, string summary, string eventId = null)
        {
            var canLogActivity = await CheckIfUserHasGrantedPermission(userId, EventCategory.ClientAdmin);
            if (canLogActivity)
            {
                var currentUser = await Db.UserProfiles.Where(u => u.UserId == userId)
                    .Select(x => x.FirstName + " " + x.LastName)
                    .FirstOrDefaultAsync();
                var activity = new ActivityDto
                {
                    Description = $"{desc} by {currentUser}",
                    ActivitySummary = summary,
                    EventCategory = EventCategory.ClientAdmin,
                    UserId = userId,
                    By = currentUser,
                    EventId = eventId,
                    Application = Applications.Joble
                };

                var dbResult = await CreateLog(activity);
                return dbResult;
            }

            return true;
        }
        #endregion
    }
}
